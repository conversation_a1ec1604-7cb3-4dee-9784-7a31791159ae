id: spectrumDetect_specDetect
label: specDetect
flags: '[python]'
category: '[spectrumDetect]'

templates:
  imports: from gnuradio import spectrumDetect
  make: spectrumDetect.specDetect(${center_frequency},${sample_rate},${vector_size}, ${n_fft}, ${trained_model}, ${augment}, ${iou}, ${save}, ${max_det}, ${debug_sigMF})

parameters:
- id: center_frequency
  label: centerFrequency
  dtype: float
  default: 2445000000.0

- id: sample_rate
  label: sampleRate
  dtype: float
  default: 20000000.0

- id: vector_size
  label: vectorSize
  dtype: int
  default: 1048576

- id: n_fft
  label: nfft
  dtype: int
  default: 1024

- id: trained_model
  label: trainedModel
  dtype: string
  default: 'detect.pt'
  
- id: augment
  label: yoloAugment
  dtype: bool
  default: False  
 
- id: iou
  label: yoloIOU
  dtype: float
  default: 0.1  
  
- id: save
  label: yoloSave
  dtype: bool
  default: False    
  
- id: max_det
  label: yoloMAX_DET
  dtype: int
  default: 300  

- id: debug_sigMF
  label: debugOut_sigMF
  dtype: bool
  default: False 

inputs:
- label: vector
  dtype: complex
  vlen: ${vector_size}

outputs:
- label: detect_pmt
  domain: message
  dtype: pmt
  vlen: 1

file_format: 1
