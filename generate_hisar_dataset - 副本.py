#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HisarMod兼容数据集生成器
使用torchsig生成与HisarMod数据集格式和结构相同的数据集
"""

import os
import torch
import numpy as np
import scipy.io as sio
import pandas as pd
from pathlib import Path
from tqdm import tqdm
import argparse
import yaml
import time
import warnings
import h5py
import traceback
import sys
from collections import OrderedDict

from torchsig.datasets.dataset_metadata import NarrowbandMetadata
from torchsig.datasets.narrowband import NewNarrowband
from torchsig.signals.signal_lists import TorchSigSignalLists
import torchsig.transforms as transforms

# 全局配置变量 - 可直接修改这些变量控制生成行为
# 设置为True表示生成相应数据集，False表示不生成
GENERATE_TRAIN = False  # 是否生成训练集
GENERATE_TEST = True   # 是否生成测试集

# 配置警告过滤
warnings.filterwarnings("ignore", category=UserWarning, module="scipy.io.matlab")
warnings.filterwarnings("ignore", category=UserWarning)

# 添加调试模式
DEBUG = True

def print_debug(message):
    """打印调试信息"""
    if DEBUG:
        print(f"[调试] {message}")

def parse_args():
    """命令行参数解析"""
    parser = argparse.ArgumentParser(description='生成HisarMod兼容格式的数据集')
    
    # 数据集基本参数
    parser.add_argument('--output_dir', type=str, default='./hisar_generated_',
                        help='输出数据集的存储路径')
    parser.add_argument('--train_samples', type=int, default=624000,
                        help='训练集样本总数量')
    parser.add_argument('--test_samples', type=int, default=208000,
                        help='测试集样本总数量')
    parser.add_argument('--samples_per_class', type=int, default=None,
                        help='每个调制类型的样本数量，如果指定则忽略train_samples和test_samples')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子，保证结果可复现')
    parser.add_argument('--batch_size', type=int, default=10000,
                        help='每批生成的样本数量，避免内存溢出')
    parser.add_argument('--sequence_length', type=int, default=2048,
                        help='每个IQ信号的序列长度')
    parser.add_argument('--snr_min', type=float, default=0,
                        help='最小信噪比(dB)')
    parser.add_argument('--snr_max', type=float, default=30,
                        help='最大信噪比(dB)')
    parser.add_argument('--snr_step', type=float, default=2.0,
                        help='信噪比步长(dB)')
    parser.add_argument('--num_workers', type=int, default=32,
                        help='数据生成的并行工作进程数')
    parser.add_argument('--overwrite', action='store_true',
                        help='是否覆盖已存在的数据集文件')
    parser.add_argument('--debug', action='store_true',
                        help='启用调试输出')
    # 添加备选调试策略
    parser.add_argument('--direct_generation', action='store_true',
                        help='使用直接信号生成方式替代数据集API')
    # 添加均衡分布选项
    parser.add_argument('--fully_balanced', action='store_true',
                        help='生成完全均衡的SNR-调制方式组合分布')
    # 添加数据集生成控制选项
    parser.add_argument('--generate_train', action='store_true',
                        help='生成训练集')
    parser.add_argument('--generate_test', action='store_true',
                        help='生成测试集')
    parser.add_argument('--generate_all', action='store_true',
                        help='生成训练集和测试集（默认行为）')
    
    return parser.parse_args()

def setup_hisar_modulation_mapping():
    """
    设置HisarMod数据集使用的调制类型映射
    返回两个字典：
    1. 调制名称到标签值的映射
    2. 调制名称到TorchSig调制类型的映射（如果torchsig支持）
    """
    # HisarMod的调制类型映射 - 已更新为25种torchsig支持的调制类型
    hisar_label_map = {
        "BPSK": 0, "QPSK": 10, "8PSK": 20, "16PSK": 30, "32PSK": 40, "64PSK": 50,
        "16QAM": 21, "32QAM": 31, "64QAM": 41, "256QAM": 61,
        "2FSK": 2, "4FSK": 12, "8FSK": 22, "16FSK": 32,
        "4ASK": 3, "8ASK": 13, "16ASK": 23, "32ASK": 33, "64ASK": 43,
        "AM-DSB": 4, "AM-DSB-SC": 14, "AM-USB": 24, "AM-LSB": 34,
        "FM": 44,
        "OOK": 64,
    }
    
    # TorchSig支持的调制类型映射到HisarMod调制类型
    torchsig_to_hisar_map = {
        # PSK系列
        "bpsk": "BPSK",
        "qpsk": "QPSK",
        "8psk": "8PSK",
        "16psk": "16PSK",
        "32psk": "32PSK",
        "64psk": "64PSK",
        
        # QAM系列
        "16qam": "16QAM",
        "32qam": "32QAM",
        "64qam": "64QAM",
        "256qam": "256QAM",
        
        # FSK系列
        "2fsk": "2FSK",
        "4fsk": "4FSK",
        "8fsk": "8FSK",
        "16fsk": "16FSK",
        
        # ASK/PAM系列 (ASK和PAM在这里是等效的)
        "4ask": "4ASK",
        "8ask": "8ASK",
        "16ask": "16ASK",
        "32ask": "32ASK",
        "64ask": "64ASK",
        
        # AM系列
        "am-dsb": "AM-DSB",
        "am-dsb-sc": "AM-DSB-SC",
        "am-usb": "AM-USB",
        "am-lsb": "AM-LSB",
        
        # FM与OOK
        "fm": "FM",
        "ook": "OOK",

    }
    
    # 检查TorchSig中可用的调制类型
    try:
        available_mods = TorchSigSignalLists.all_signals
        print_debug(f"可用的调制类型总数: {len(available_mods)}")
        print_debug(f"部分可用调制类型: {list(available_mods)[:10]}...")
    except Exception as e:
        print(f"获取TorchSig调制类型列表失败: {e}")
        # 提供一个基本的调制类型列表，以防获取失败
        available_mods = {"bpsk", "qpsk", "16qam", "64qam", "am-dsb", "fm"}
    
    # 为缺失的调制类型提供合理的替代
    torchsig_replacements = {}
    reverse_map = {}
    
    # 检查哪些调制类型在torchsig中不可用，并找替代
    for torchsig_mod, hisar_mod in torchsig_to_hisar_map.items():
        if torchsig_mod not in available_mods:
            # 尝试找相似的替代
            if torchsig_mod == "pm":
                alt_mod = "fm"     # 用FM替代PM，两者很相似
            elif torchsig_mod == "ook":
                alt_mod = "2ask"   # 尝试使用2ASK替代OOK
            elif "ask" in torchsig_mod:
                # 对于不可用的ASK调制，使用PAM作为替代
                order = torchsig_mod.strip("ask")
                alt_mod = f"{order}pam" if f"{order}pam" in available_mods else "4pam"
            else:
                # 尝试找同族的其他调制
                mod_family = torchsig_mod.split("-")[0] if "-" in torchsig_mod else torchsig_mod.rstrip("0123456789qamskp")
                possible_alts = [m for m in available_mods if mod_family in m]
                alt_mod = possible_alts[0] if possible_alts else "qpsk"  # 默认使用QPSK
            
            torchsig_replacements[torchsig_mod] = alt_mod
            print(f"警告: TorchSig中没有'{torchsig_mod}'，将使用'{alt_mod}'替代生成'{hisar_mod}'")
        
        # 记录可用的映射
        actual_mod = torchsig_replacements.get(torchsig_mod, torchsig_mod)
        reverse_map[hisar_mod] = actual_mod
    
    return hisar_label_map, reverse_map

def generate_snr_distribution(snr_min, snr_max, snr_step, num_samples):
    """
    生成SNR值分布
    
    参数:
        snr_min: 最小SNR值
        snr_max: 最大SNR值
        snr_step: SNR步长
        num_samples: 总样本数
        
    返回:
        numpy数组，包含所有样本的SNR值
    """
    snr_values = np.arange(snr_min, snr_max + 0.1, snr_step)
    num_snr_levels = len(snr_values)
    
    # 计算每个SNR的样本数量，确保均匀分布
    samples_per_snr = num_samples // num_snr_levels
    remainder = num_samples % num_snr_levels
    
    # 分配样本数量
    snr_counts = [samples_per_snr] * num_snr_levels
    for i in range(remainder):
        snr_counts[i] += 1
    
    # 生成SNR分布
    snr_distribution = []
    for i, count in enumerate(snr_counts):
        snr_distribution.extend([snr_values[i]] * count)
    
    # 再次打乱以确保随机性
    rng = np.random.RandomState(42)
    rng.shuffle(snr_distribution)
    
    return np.array(snr_distribution)

def generate_labels_distribution(hisar_label_map, num_samples, balanced=True):
    """
    生成标签分布
    
    参数:
        hisar_label_map: 调制类型到标签值的映射
        num_samples: 总样本数
        balanced: 是否生成均衡的数据集
        
    返回:
        numpy数组，包含所有样本的标签
    """
    label_values = list(hisar_label_map.values())
    num_classes = len(label_values)
    
    if balanced:
        # 均衡分布
        samples_per_class = num_samples // num_classes
        remainder = num_samples % num_classes
        
        # 分配样本数量
        class_counts = [samples_per_class] * num_classes
        for i in range(remainder):
            class_counts[i] += 1
        
        # 生成标签分布
        label_distribution = []
        for i, count in enumerate(class_counts):
            label_distribution.extend([label_values[i]] * count)
    else:
        # 随机分布（不推荐）
        label_distribution = np.random.choice(label_values, num_samples)
    
    # 打乱以确保随机性
    rng = np.random.RandomState(42)
    rng.shuffle(label_distribution)
    
    return np.array(label_distribution)

def create_signal_directly(modulation_type, snr_db, sequence_length, seed):
    """
    直接使用torchsig生成信号，跳过数据集API
    
    参数:
        modulation_type: 调制类型名称
        snr_db: 信噪比(dB)
        sequence_length: 信号长度
        seed: 随机种子
        
    返回:
        numpy数组，形状为(2, sequence_length)的IQ信号
    """
    torch.manual_seed(seed)
    np.random.seed(seed)
    
    try:
        # 导入信号生成模块
        from torchsig.signals.modulations import analog_modulation, digital_modulation
        from torchsig.signals.siggen import addhierarchicalnoise
        
        # 根据调制类型选择生成函数
        if modulation_type in ["am-dsb", "am-dsb-sc", "am-usb", "am-lsb", "fm"]:
            # 模拟调制
            waveform_params = {
                "carrier_freq": 0.15,  # 载波频率
                "am_mod_idx": 0.8,     # 调制指数(用于AM)
                "fm_mod_idx": 0.05     # 调制指数(用于FM)
            }
            
            # 生成模拟调制信号
            if "am" in modulation_type:
                am_type = modulation_type.split("-")[1] if len(modulation_type.split("-")) > 1 else "dsb"
                signal = analog_modulation(sequence_length, modulation_type="am", 
                                          am_type=am_type, **waveform_params)
            else:  # FM
                signal = analog_modulation(sequence_length, modulation_type="fm", **waveform_params)
        else:
            # 数字调制
            mod_type = modulation_type.strip("0123456789_-")  # 提取基本类型(psk, qam等)
            
            # 确定调制阶数
            if modulation_type.startswith("8"):
                constellation_order = 8
            elif modulation_type.startswith("16"):
                constellation_order = 16
            elif modulation_type.startswith("32"):
                constellation_order = 32
            elif modulation_type.startswith("64"):
                constellation_order = 64
            elif modulation_type.startswith("128"):
                constellation_order = 128
            elif modulation_type.startswith("256"):
                constellation_order = 256
            elif modulation_type.startswith("4"):
                constellation_order = 4
            elif modulation_type.startswith("2"):
                constellation_order = 2
            else:
                # 默认QPSK/BPSK
                constellation_order = 4 if mod_type == "qam" or mod_type == "psk" else 2
            
            # 生成数字调制信号
            waveform_params = {
                "samples_per_symbol": 8,
                "pulse_shape": "rrc",
                "sps": 8,
                "alpha": 0.35
            }
            
            signal = digital_modulation(sequence_length, modulation_type=mod_type,
                                      constellation_order=constellation_order,
                                      **waveform_params)
        
        # 添加噪声
        noisy_signal = addhierarchicalnoise(signal, snr_db=snr_db)
        
        # 确保形状正确并转为浮点数
        if isinstance(noisy_signal, torch.Tensor):
            noisy_signal = noisy_signal.numpy()
        
        # 处理复数信号 - 这是修改的主要部分
        if np.iscomplexobj(noisy_signal):
            # 如果是复数信号，分离为I/Q分量
            i_component = np.real(noisy_signal)
            q_component = np.imag(noisy_signal)
            
            # 确保I/Q分量形状一致
            if len(i_component.shape) == 1:
                iq_signal = np.stack([i_component, q_component])
            else:
                # 如果已经是多维的，则需确保转置正确
                iq_signal = np.stack([i_component.flatten(), q_component.flatten()])
                # 裁剪或填充到目标长度
                if iq_signal.shape[1] > sequence_length:
                    iq_signal = iq_signal[:, :sequence_length]
                elif iq_signal.shape[1] < sequence_length:
                    padding = np.zeros((2, sequence_length - iq_signal.shape[1]), dtype=np.float32)
                    iq_signal = np.concatenate([iq_signal, padding], axis=1)
        else:
            # 非复数信号的处理，保持原逻辑
            if len(noisy_signal.shape) == 1:
                # 创建IQ信号(将实信号转为复信号)
                i_component = noisy_signal
                q_component = np.zeros_like(i_component)
                iq_signal = np.stack([i_component, q_component])
            else:
                # 已经是IQ信号
                iq_signal = noisy_signal
            
            # 确保形状是(2, sequence_length)
            if iq_signal.shape[0] != 2:
                iq_signal = iq_signal.T
        
        # 规范化信号
        iq_signal = iq_signal.astype(np.float32)
        if np.std(iq_signal) > 0:
            iq_signal = iq_signal / np.std(iq_signal)
        
        return iq_signal
    
    except Exception as e:
        print(f"直接生成信号时出错: {str(e)}")
        print(f"异常类型: {type(e).__name__}")
        print(f"详细异常信息: {traceback.format_exc()}")
        
        # 生成随机信号作为替代
        random_signal = np.random.normal(0, 1, (2, sequence_length)).astype(np.float32)
        random_signal = random_signal / np.std(random_signal)
        return random_signal

def inspect_dataset_structure(dataset, index=0):
    """
    检查数据集结构以便调试
    
    参数:
        dataset: TorchSig数据集对象
        index: 要检查的样本索引
    """
    try:
        print_debug(f"数据集类型: {type(dataset)}")
        print_debug(f"数据集长度: {len(dataset)}")
        
        # 尝试获取样本并检查类型
        sample = dataset[index]
        print_debug(f"样本类型: {type(sample)}")
        
        if isinstance(sample, tuple):
            print_debug(f"样本是元组: 长度={len(sample)}")
            for i, item in enumerate(sample):
                print_debug(f"  元素[{i}] 类型: {type(item)}")
                if isinstance(item, (np.ndarray, torch.Tensor)):
                    print_debug(f"  元素[{i}] 形状: {item.shape}")
        elif isinstance(sample, (np.ndarray, torch.Tensor)):
            print_debug(f"样本是数组/张量: 形状={sample.shape}")
        else:
            print_debug(f"样本是其他类型")
            
    except Exception as e:
        print_debug(f"检查数据集结构时出错: {str(e)}")

def generate_balanced_distributions(hisar_label_map, snr_min, snr_max, snr_step, num_samples):
    """
    生成SNR和标签的均衡分布，确保每种SNR-调制方式组合的样本数量相同
    
    参数:
        hisar_label_map: 调制类型到标签值的映射
        snr_min: 最小SNR值
        snr_max: 最大SNR值
        snr_step: SNR步长
        num_samples: 总样本数
        
    返回:
        两个numpy数组，分别包含所有样本的SNR值和标签
    """
    snr_values = np.arange(snr_min, snr_max + 0.1, snr_step)
    label_values = list(hisar_label_map.values())
    
    num_snrs = len(snr_values)
    num_mods = len(label_values)
    
    # 计算每个组合的样本数
    samples_per_combo = num_samples // (num_snrs * num_mods)
    remainder = num_samples % (num_snrs * num_mods)
    
    # 生成分布
    snr_distribution = []
    label_distribution = []
    
    # 为每个组合分配基本数量的样本
    for snr in snr_values:
        for label in label_values:
            snr_distribution.extend([snr] * samples_per_combo)
            label_distribution.extend([label] * samples_per_combo)
    
    # 分配余数
    combo_idx = 0
    for _ in range(remainder):
        snr_idx = combo_idx // num_mods
        label_idx = combo_idx % num_mods
        snr_distribution.append(snr_values[snr_idx])
        label_distribution.append(label_values[label_idx])
        combo_idx = (combo_idx + 1) % (num_snrs * num_mods)
    
    # 打乱以确保随机性，但使用相同的排列以保持SNR和标签对应
    indices = np.arange(len(snr_distribution))
    rng = np.random.RandomState(42)
    rng.shuffle(indices)
    
    snr_distribution = np.array(snr_distribution)[indices]
    label_distribution = np.array(label_distribution)[indices]
    
    return snr_distribution, label_distribution

def generate_and_save_hisar_dataset(args, split_name):
    """
    生成并保存HisarMod格式的数据集
    
    参数:
        args: 命令行参数
        split_name: 数据集划分名称（'train'或'test'）
    """
    # 设置调试模式
    global DEBUG
    DEBUG = args.debug
    
    # 确定样本数量
    num_samples = args.train_samples if split_name == 'train' else args.test_samples
    
    # 设置HisarMod的调制类型映射
    hisar_label_map, hisar_to_torchsig_map = setup_hisar_modulation_mapping()
    
    # 生成SNR值和标签分布
    if args.fully_balanced:
        # 使用完全均衡的SNR-调制方式组合分布
        snr_distribution, label_distribution = generate_balanced_distributions(
            hisar_label_map, args.snr_min, args.snr_max, args.snr_step, num_samples
        )
        print(f"使用完全均衡的SNR-调制方式组合分布生成{split_name}数据集")
    else:
        # 使用原来的各自均衡分布
        snr_distribution = generate_snr_distribution(args.snr_min, args.snr_max, args.snr_step, num_samples)
        label_distribution = generate_labels_distribution(hisar_label_map, num_samples)
        print(f"使用各自均衡的SNR和调制方式分布生成{split_name}数据集")
    
    # 创建输出目录
    output_path = Path(args.output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 定义输出文件路径
    data_file = output_path / f"{split_name}_data.mat"
    labels_file = output_path / f"{split_name}_labels.csv"
    snr_file = output_path / f"{split_name}_snr.csv"
    
    # 检查文件是否已存在
    if not args.overwrite and all(f.exists() for f in [data_file, labels_file, snr_file]):
        print(f"{split_name}数据集已存在，跳过生成。使用--overwrite参数可强制重新生成。")
        return
    
    # 保存SNR和标签
    np.savetxt(snr_file, snr_distribution, fmt="%.1f", delimiter=',')
    np.savetxt(labels_file, label_distribution, fmt="%d", delimiter=',')
    print(f"已保存{split_name}标签和SNR文件")
    
    # 使用h5py创建MAT文件，便于逐批生成大型文件
    with h5py.File(data_file, 'w') as f:
        # 创建数据集占位
        dset = f.create_dataset('data', shape=(num_samples, 2, args.sequence_length),
                                 dtype=np.float32, chunks=True)
        
        # 按批次生成数据
        batch_size = args.batch_size
        num_batches = (num_samples + batch_size - 1) // batch_size
        
        # 记录成功和失败的样本数
        success_count = 0
        error_count = 0
        error_types = {}
        
        for batch_idx in tqdm(range(num_batches), desc=f"生成{split_name}数据"):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, num_samples)
            curr_batch_size = end_idx - start_idx
            
            # 获取当前批次的SNR和标签
            curr_snrs = snr_distribution[start_idx:end_idx]
            curr_labels = label_distribution[start_idx:end_idx]
            
            # 为每个样本生成信号
            batch_data = np.zeros((curr_batch_size, 2, args.sequence_length), dtype=np.float32)
            
            for i in range(curr_batch_size):
                # 获取当前样本的调制类型和SNR
                label_value = curr_labels[i]
                snr_value = curr_snrs[i]
                
                # 找到HisarMod调制类型
                hisar_mod = None
                for mod, val in hisar_label_map.items():
                    if val == label_value:
                        hisar_mod = mod
                        break
                
                if hisar_mod is None:
                    print(f"警告: 未找到标签值{label_value}对应的调制类型")
                    hisar_mod = "QPSK"  # 默认使用QPSK
                
                # 获取torchsig中对应的调制类型
                torchsig_mod = hisar_to_torchsig_map.get(hisar_mod)
                
                if torchsig_mod is None:
                    print(f"警告: 未找到{hisar_mod}对应的TorchSig调制类型")
                    torchsig_mod = "qpsk"  # 默认使用QPSK
                
                if args.direct_generation:
                    # 使用直接生成方法
                    try:
                        signal = create_signal_directly(
                            torchsig_mod, 
                            snr_value, 
                            args.sequence_length,
                            args.seed + start_idx + i
                        )
                        
                        # 修改：确保signal是实数的IQ表示
                        if np.iscomplexobj(signal):
                            i_component = np.real(signal)
                            q_component = np.imag(signal)
                            signal = np.stack([i_component, q_component])
                        
                        batch_data[i] = signal
                        success_count += 1
                    except Exception as e:
                        error_msg = str(e)
                        print(f"直接生成信号时出错: {error_msg}")
                        error_type = type(e).__name__
                        if error_type not in error_types:
                            error_types[error_type] = 0
                        error_types[error_type] += 1
                        error_count += 1
                        
                        # 生成随机信号作为替代
                        random_signal = np.random.normal(0, 1, (2, args.sequence_length)).astype(np.float32)
                        random_signal = random_signal / np.std(random_signal)
                        batch_data[i] = random_signal
                else:
                    # 使用数据集API方法
                    try:
                        # 创建元数据
                        metadata = NarrowbandMetadata(
                            num_iq_samples_dataset=args.sequence_length,
                            fft_size=64,  # 这个值不重要，因为我们只需要IQ样本
                            impairment_level=1,  # 中等水平的信号损伤
                            num_samples=1,  # 只生成一个样本
                            class_list=[torchsig_mod],  # 只使用指定的调制类型
                            snr_db_min=snr_value,  # 固定SNR
                            snr_db_max=snr_value,  # 固定SNR
                            seed=args.seed + start_idx + i  # 独特的种子确保样本多样性
                        )
                        
                        # 创建窄带数据集并安全获取信号
                        try:
                            print_debug(f"创建数据集: 调制={torchsig_mod}, SNR={snr_value}dB")
                            narrowband_dataset = NewNarrowband(dataset_metadata=metadata)
                            
                            # 检查数据集结构
                            if args.debug and i == 0 and batch_idx == 0:
                                inspect_dataset_structure(narrowband_dataset)
                            
                            # 根据torchsig实现分析，由__getitem__返回的格式为Tuple[np.ndarray, Tuple]
                            # 即(数据, 目标)
                            try:
                                # 直接获取数据和目标
                                data, targets = narrowband_dataset[0]
                                
                                print_debug(f"获取到的数据: 类型={type(data)}, 形状={data.shape if hasattr(data, 'shape') else 'Unknown'}")
                                print_debug(f"获取到的目标: 类型={type(targets)}")
                                
                                # 确保数据是numpy数组或torch张量
                                if isinstance(data, torch.Tensor):
                                    signal = data.numpy()
                                elif isinstance(data, np.ndarray):
                                    signal = data
                                else:
                                    raise TypeError(f"数据类型错误：{type(data)}")
                                
                                # 修改：处理复数信号
                                if np.iscomplexobj(signal):
                                    i_component = np.real(signal)
                                    q_component = np.imag(signal)
                                    if len(i_component.shape) == 1:
                                        signal = np.stack([i_component, q_component])
                                    else:
                                        # 如果已经是多维的，需确保维度正确
                                        signal = np.stack([i_component.flatten(), q_component.flatten()])
                                
                                # 确保形状正确，如果需要裁剪或填充
                                if len(signal.shape) > 1 and signal.shape[1] > args.sequence_length:
                                    # 裁剪
                                    signal = signal[:, :args.sequence_length]
                                elif len(signal.shape) > 1 and signal.shape[1] < args.sequence_length:
                                    # 填充
                                    padding = np.zeros((2, args.sequence_length - signal.shape[1]), dtype=np.float32)
                                    signal = np.concatenate([signal, padding], axis=1)
                                
                                # 保存到批次数据
                                batch_data[i] = signal
                                success_count += 1
                                
                            except Exception as e:
                                error_msg = str(e)
                                print(f"获取数据时出错: {error_msg}")
                                print(f"详细异常: {traceback.format_exc()}")
                                error_type = type(e).__name__
                                if error_type not in error_types:
                                    error_types[error_type] = 0
                                error_types[error_type] += 1
                                error_count += 1
                                
                                # 生成随机信号作为替代
                                random_signal = np.random.normal(0, 1, (2, args.sequence_length)).astype(np.float32)
                                random_signal = random_signal / np.std(random_signal)
                                batch_data[i] = random_signal
                                
                        except Exception as e:
                            error_msg = str(e)
                            print(f"创建数据集时出错: {error_msg}")
                            error_type = type(e).__name__
                            if error_type not in error_types:
                                error_types[error_type] = 0
                            error_types[error_type] += 1
                            error_count += 1
                            
                            # 生成随机信号作为替代
                            random_signal = np.random.normal(0, 1, (2, args.sequence_length)).astype(np.float32)
                            random_signal = random_signal / np.std(random_signal)
                            batch_data[i] = random_signal
                    except Exception as e:
                        error_msg = str(e)
                        print(f"生成样本时出错: {error_msg}")
                        error_type = type(e).__name__
                        if error_type not in error_types:
                            error_types[error_type] = 0
                        error_types[error_type] += 1
                        error_count += 1
                        
                        # 生成随机信号作为替代
                        random_signal = np.random.normal(0, 1, (2, args.sequence_length)).astype(np.float32)
                        random_signal = random_signal / np.std(random_signal)
                        batch_data[i] = random_signal
            
            # 将批次数据写入数据集
            dset[start_idx:end_idx] = batch_data
        
        # 打印错误统计
        print(f"成功生成样本: {success_count}/{num_samples} ({success_count/num_samples*100:.1f}%)")
        if error_count > 0:
            print(f"生成失败样本: {error_count}/{num_samples} ({error_count/num_samples*100:.1f}%)")
            print("错误类型统计:")
            for error_type, count in error_types.items():
                print(f"  {error_type}: {count}次")
    
    print(f"已生成并保存{split_name}数据集: {num_samples}个样本")

def create_config_file(args):
    """
    创建配置文件，与原始HisarMod数据集配置兼容
    """
    config = {
        "data": {
            "dataset_type": "hisar",
            "train_path": os.path.join(args.output_dir, "train_data.mat"),
            "test_path": os.path.join(args.output_dir, "test_data.mat"),
            "train_labels_path": os.path.join(args.output_dir, "train_labels.csv"),
            "test_labels_path": os.path.join(args.output_dir, "test_labels.csv"),
            "train_snr_path": os.path.join(args.output_dir, "train_snr.csv"),
            "test_snr_path": os.path.join(args.output_dir, "test_snr.csv"),
            "val_ratio": 0.2,
            "snr_range": [args.snr_min, args.snr_max, args.snr_step],
            "normalize": True,
            "augment": False
        },
        "model": {
            "type": "wnn_mrnn",
            "sequence_length": args.sequence_length,
            "num_classes": 26  # HisarMod数据集的类别数
        },
        "training": {
            "batch_size": 128,
            "epochs": 100,
            "learning_rate": 0.001,
            "weight_decay": 1e-5,
            "lr_scheduler": "step",
            "lr_step_size": 30,
            "lr_gamma": 0.1
        }
    }
    
    # 保存配置文件
    config_path = Path(args.output_dir) / "config.yaml"
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print(f"已生成配置文件: {config_path}")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置调试模式
    global DEBUG
    DEBUG = args.debug
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    start_time = time.time()
    
    print("=== HisarMod兼容数据集生成配置 ===")
    print(f"输出目录: {args.output_dir}")
    print(f"训练集样本数: {args.train_samples}")
    print(f"测试集样本数: {args.test_samples}")
    print(f"序列长度: {args.sequence_length}")
    print(f"信噪比范围: {args.snr_min}dB 到 {args.snr_max}dB，步长 {args.snr_step}dB")
    print(f"调试模式: {'开启' if args.debug else '关闭'}")
    print(f"信号生成方式: {'直接生成' if args.direct_generation else '数据集API'}")
    print(f"数据分布: {'完全均衡SNR-调制方式组合' if args.fully_balanced else '各自均衡'}")
    print("===============================")
    
    # 使用全局配置变量控制数据集生成
    global GENERATE_TRAIN, GENERATE_TEST
    
    # 输出当前配置
    print(f"生成训练集: {'是' if GENERATE_TRAIN else '否'}")
    print(f"生成测试集: {'是' if GENERATE_TEST else '否'}")
    print("===============================")
    
    # 生成训练集
    if GENERATE_TRAIN:
        print("生成训练集...")
        generate_and_save_hisar_dataset(args, 'train')
    
    # 生成测试集
    if GENERATE_TEST:
        print("生成测试集...")
        generate_and_save_hisar_dataset(args, 'test')
    
    # 创建配置文件
    create_config_file(args)
    
    end_time = time.time()
    print(f"数据集生成完成! 用时: {(end_time - start_time):.2f}秒")
    print(f"数据集保存在: {args.output_dir}")

if __name__ == "__main__":
    main() 