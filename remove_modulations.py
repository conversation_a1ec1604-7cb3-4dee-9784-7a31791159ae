#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调制方式删除工具
从已生成的HisarMod格式数据集中删除指定的调制方式，保持原有数据和文件结构

使用方法:
1. 修改下面配置区域中的参数
2. 直接运行: python remove_modulations.py

配置说明:
- DATASET_DIR: 数据集目录路径
- MODULATIONS_TO_REMOVE: 要删除的调制方式列表
- OUTPUT_DIR: 输出目录（None表示覆盖原目录）
- BACKUP_ORIGINAL: 是否备份原始数据
- ANALYZE_ONLY: 是否仅分析而不删除

支持的调制方式:
PSK: BPSK, QPSK, 8PSK, 16PSK, 32PSK, 64PSK
QAM: 16QAM, 32QAM, 64QAM, 256QAM
FSK: 2FSK, 4FSK, 8FSK, 16FSK
ASK: 4ASK, 8ASK, 16ASK, 32ASK, 64ASK
AM:  AM-DSB, AM-DSB-SC, AM-USB, AM-LSB
其他: FM, OOK
"""

import os
import numpy as np
import pandas as pd
import h5py
from pathlib import Path
from tqdm import tqdm
import shutil
from collections import Counter

# ==================== 配置区域 ====================
# 在这里直接修改配置，无需使用命令行参数

# 数据集路径
DATASET_DIR = "hisar_filtered"

# 要删除的调制方式列表（在这里添加或删除调制方式）
MODULATIONS_TO_REMOVE = [
    "BPSK",     # 删除BPSK
    "QPSK",     # 删除QPSK
    # "8PSK",   # 取消注释以删除8PSK
    # "16QAM",  # 取消注释以删除16QAM
    # "FM",     # 取消注释以删除FM
    # "OOK",    # 取消注释以删除OOK
]

# 常用配置示例（取消注释使用）:
#
# 示例1: 删除低阶调制方式
# MODULATIONS_TO_REMOVE = ["BPSK", "QPSK"]
#
# 示例2: 删除所有FSK调制
# MODULATIONS_TO_REMOVE = ["2FSK", "4FSK", "8FSK", "16FSK"]
#
# 示例3: 删除高阶和不常用调制
# MODULATIONS_TO_REMOVE = ["32PSK", "64PSK", "256QAM", "32ASK", "64ASK", "OOK"]
#
# 示例4: 只保留PSK调制（删除其他所有调制）
# MODULATIONS_TO_REMOVE = [
#     "16QAM", "32QAM", "64QAM", "256QAM",           # 删除QAM
#     "2FSK", "4FSK", "8FSK", "16FSK",               # 删除FSK
#     "4ASK", "8ASK", "16ASK", "32ASK", "64ASK",     # 删除ASK
#     "AM-DSB", "AM-DSB-SC", "AM-USB", "AM-LSB",     # 删除AM
#     "FM", "OOK"                                     # 删除其他
# ]

# 输出目录（如果为None则覆盖原目录）
OUTPUT_DIR = "hisar_filtered"  # 设置为None可覆盖原目录

# 是否备份原始数据（仅在覆盖原目录时有效）
BACKUP_ORIGINAL = True

# 是否仅分析数据集而不执行删除操作
ANALYZE_ONLY = True  # 先设置为True进行分析

# ==================== 配置区域结束 ====================

def get_hisar_modulation_mapping():
    """
    获取HisarMod数据集的调制类型映射
    返回调制名称到标签值的映射字典
    """
    hisar_label_map = {
        "BPSK": 0, "QPSK": 10, "8PSK": 20, "16PSK": 30, "32PSK": 40, "64PSK": 50,
        "16QAM": 21, "32QAM": 31, "64QAM": 41, "256QAM": 61,
        "2FSK": 2, "4FSK": 12, "8FSK": 22, "16FSK": 32,
        "4ASK": 3, "8ASK": 13, "16ASK": 23, "32ASK": 33, "64ASK": 43,
        "AM-DSB": 4, "AM-DSB-SC": 14, "AM-USB": 24, "AM-LSB": 34,
        "FM": 44,
        "OOK": 64,
    }
    
    # 创建反向映射：标签值到调制名称
    label_to_mod = {v: k for k, v in hisar_label_map.items()}
    
    return hisar_label_map, label_to_mod

def analyze_dataset(dataset_dir):
    """
    分析数据集中的调制方式分布
    
    参数:
        dataset_dir: 数据集目录路径
        
    返回:
        dict: 包含训练集和测试集调制方式统计的字典
    """
    dataset_path = Path(dataset_dir)
    hisar_label_map, label_to_mod = get_hisar_modulation_mapping()
    
    analysis = {}
    
    for split in ['train', 'test']:
        labels_file = dataset_path / f"{split}_labels.csv"
        if labels_file.exists():
            # 读取标签
            labels = np.loadtxt(labels_file, delimiter=',', dtype=int)
            
            # 统计每种调制方式的数量
            label_counts = Counter(labels)
            mod_counts = {}
            
            for label, count in label_counts.items():
                mod_name = label_to_mod.get(label, f"Unknown_{label}")
                mod_counts[mod_name] = count
            
            analysis[split] = {
                'total_samples': len(labels),
                'modulation_counts': mod_counts,
                'unique_labels': sorted(label_counts.keys())
            }
        else:
            print(f"警告: 未找到 {labels_file}")
            analysis[split] = None
    
    return analysis

def remove_modulations_from_dataset(dataset_dir, modulations_to_remove, output_dir=None, backup=True):
    """
    从数据集中删除指定的调制方式
    
    参数:
        dataset_dir: 原始数据集目录
        modulations_to_remove: 要删除的调制方式列表
        output_dir: 输出目录，如果为None则覆盖原目录
        backup: 是否备份原始数据
        
    返回:
        dict: 处理结果统计
    """
    dataset_path = Path(dataset_dir)
    hisar_label_map, label_to_mod = get_hisar_modulation_mapping()
    
    # 获取要删除的标签值
    labels_to_remove = set()
    for mod in modulations_to_remove:
        if mod in hisar_label_map:
            labels_to_remove.add(hisar_label_map[mod])
        else:
            print(f"警告: 未知的调制方式 '{mod}'")
    
    if not labels_to_remove:
        print("错误: 没有有效的调制方式需要删除")
        return None
    
    print(f"将删除以下调制方式: {modulations_to_remove}")
    print(f"对应的标签值: {sorted(labels_to_remove)}")
    
    # 设置输出目录
    if output_dir is None:
        output_path = dataset_path
        if backup:
            backup_path = dataset_path.parent / f"{dataset_path.name}_backup"
            if backup_path.exists():
                shutil.rmtree(backup_path)
            shutil.copytree(dataset_path, backup_path)
            print(f"已备份原始数据到: {backup_path}")
    else:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
    
    results = {}
    
    # 处理训练集和测试集
    for split in ['train', 'test']:
        print(f"\n处理 {split} 数据集...")
        
        # 文件路径
        data_file = dataset_path / f"{split}_data.mat"
        labels_file = dataset_path / f"{split}_labels.csv"
        snr_file = dataset_path / f"{split}_snr.csv"
        
        output_data_file = output_path / f"{split}_data.mat"
        output_labels_file = output_path / f"{split}_labels.csv"
        output_snr_file = output_path / f"{split}_snr.csv"
        
        if not all(f.exists() for f in [data_file, labels_file, snr_file]):
            print(f"跳过 {split} 数据集: 文件不完整")
            continue
        
        # 读取标签和SNR
        labels = np.loadtxt(labels_file, delimiter=',', dtype=int)
        snrs = np.loadtxt(snr_file, delimiter=',', dtype=float)
        
        # 找到要保留的样本索引
        keep_indices = []
        for i, label in enumerate(labels):
            if label not in labels_to_remove:
                keep_indices.append(i)
        
        keep_indices = np.array(keep_indices)
        
        print(f"原始样本数: {len(labels)}")
        print(f"保留样本数: {len(keep_indices)}")
        print(f"删除样本数: {len(labels) - len(keep_indices)}")
        
        if len(keep_indices) == 0:
            print(f"警告: {split} 数据集中所有样本都被删除了!")
            continue
        
        # 过滤标签和SNR
        filtered_labels = labels[keep_indices]
        filtered_snrs = snrs[keep_indices]
        
        # 处理数据文件
        print("处理数据文件...")
        with h5py.File(data_file, 'r') as src_f:
            data_shape = src_f['data'].shape
            print(f"原始数据形状: {data_shape}")
            
            # 创建新的数据文件
            with h5py.File(output_data_file, 'w') as dst_f:
                # 创建过滤后的数据集
                filtered_data = dst_f.create_dataset(
                    'data', 
                    shape=(len(keep_indices), data_shape[1], data_shape[2]),
                    dtype=src_f['data'].dtype,
                    chunks=True
                )
                
                # 分批复制数据以节省内存
                batch_size = 1000
                for i in tqdm(range(0, len(keep_indices), batch_size), desc="复制数据"):
                    end_idx = min(i + batch_size, len(keep_indices))
                    batch_indices = keep_indices[i:end_idx]
                    
                    # 读取原始数据批次
                    batch_data = src_f['data'][batch_indices]
                    
                    # 写入过滤后的数据
                    filtered_data[i:end_idx] = batch_data
        
        # 保存过滤后的标签和SNR
        np.savetxt(output_labels_file, filtered_labels, fmt='%d', delimiter=',')
        np.savetxt(output_snr_file, filtered_snrs, fmt='%.1f', delimiter=',')
        
        # 统计结果
        original_mod_counts = Counter(labels)
        filtered_mod_counts = Counter(filtered_labels)
        
        results[split] = {
            'original_samples': len(labels),
            'filtered_samples': len(filtered_labels),
            'removed_samples': len(labels) - len(filtered_labels),
            'original_modulations': {label_to_mod.get(k, f"Unknown_{k}"): v for k, v in original_mod_counts.items()},
            'filtered_modulations': {label_to_mod.get(k, f"Unknown_{k}"): v for k, v in filtered_mod_counts.items()}
        }
        
        print(f"✅ {split} 数据集处理完成")
    
    # 复制配置文件
    config_file = dataset_path / "config.yaml"
    if config_file.exists() and output_path != dataset_path:
        shutil.copy2(config_file, output_path / "config.yaml")
        print("✅ 配置文件已复制")
    
    return results

def print_analysis_report(analysis):
    """打印数据集分析报告"""
    print("\n" + "="*60)
    print("📊 数据集分析报告")
    print("="*60)
    
    hisar_label_map, label_to_mod = get_hisar_modulation_mapping()
    
    for split, data in analysis.items():
        if data is None:
            continue
            
        print(f"\n📋 {split.upper()} 数据集:")
        print(f"总样本数: {data['total_samples']:,}")
        print(f"调制方式数: {len(data['modulation_counts'])}")
        
        print("\n调制方式分布:")
        for mod, count in sorted(data['modulation_counts'].items()):
            percentage = count / data['total_samples'] * 100
            print(f"  {mod:>10}: {count:>8,} ({percentage:5.1f}%)")

def print_removal_report(results):
    """打印删除操作报告"""
    print("\n" + "="*60)
    print("🗑️  调制方式删除报告")
    print("="*60)
    
    for split, data in results.items():
        print(f"\n📋 {split.upper()} 数据集:")
        print(f"原始样本数: {data['original_samples']:,}")
        print(f"保留样本数: {data['filtered_samples']:,}")
        print(f"删除样本数: {data['removed_samples']:,}")
        print(f"删除比例: {data['removed_samples']/data['original_samples']*100:.1f}%")
        
        print(f"\n删除后的调制方式分布:")
        for mod, count in sorted(data['filtered_modulations'].items()):
            percentage = count / data['filtered_samples'] * 100
            print(f"  {mod:>10}: {count:>8,} ({percentage:5.1f}%)")

def main():
    """
    主函数 - 使用文件顶部的配置进行操作
    """
    print("=" * 60)
    print("🗑️  调制方式删除工具")
    print("=" * 60)

    # 显示当前配置
    print("📋 当前配置:")
    print(f"  数据集目录: {DATASET_DIR}")
    print(f"  要删除的调制方式: {MODULATIONS_TO_REMOVE}")
    print(f"  输出目录: {OUTPUT_DIR if OUTPUT_DIR else '覆盖原目录'}")
    print(f"  备份原始数据: {'是' if BACKUP_ORIGINAL else '否'}")
    print(f"  仅分析模式: {'是' if ANALYZE_ONLY else '否'}")
    print("-" * 60)

    # 检查数据集目录
    if not Path(DATASET_DIR).exists():
        print(f"❌ 错误: 数据集目录不存在: {DATASET_DIR}")
        print("请检查配置中的 DATASET_DIR 路径是否正确")
        return

    # 检查要删除的调制方式列表
    if not MODULATIONS_TO_REMOVE:
        print("⚠️  警告: 没有指定要删除的调制方式")
        print("请在配置中的 MODULATIONS_TO_REMOVE 列表中添加要删除的调制方式")
        return

    # 分析数据集
    print("🔍 分析数据集...")
    analysis = analyze_dataset(DATASET_DIR)
    print_analysis_report(analysis)

    if ANALYZE_ONLY:
        print("\n✅ 仅分析模式，操作完成")
        print("如需执行删除操作，请将配置中的 ANALYZE_ONLY 设置为 False")
        return

    # 确认操作
    print(f"\n⚠️  即将删除以下调制方式: {MODULATIONS_TO_REMOVE}")
    if OUTPUT_DIR:
        print(f"结果将保存到: {OUTPUT_DIR}")
    else:
        print("将覆盖原始数据集" + ("（已启用备份）" if BACKUP_ORIGINAL else "（未启用备份）"))

    # 执行删除操作
    print(f"\n🗑️  开始删除调制方式...")
    results = remove_modulations_from_dataset(
        DATASET_DIR,
        MODULATIONS_TO_REMOVE,
        OUTPUT_DIR,
        backup=BACKUP_ORIGINAL
    )

    if results:
        print_removal_report(results)
        print(f"\n✅ 操作完成! 输出目录: {OUTPUT_DIR or DATASET_DIR}")
    else:
        print("\n❌ 操作失败")

if __name__ == "__main__":
    main()
