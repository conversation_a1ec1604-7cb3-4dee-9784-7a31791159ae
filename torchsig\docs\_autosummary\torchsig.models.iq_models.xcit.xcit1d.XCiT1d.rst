torchsig.models.iq\_models.xcit.xcit1d.XCiT1d
=============================================

.. currentmodule:: torchsig.models.iq_models.xcit.xcit1d

.. autoclass:: XCiT1d
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~XCiT1d.add_module
      ~XCiT1d.apply
      ~XCiT1d.bfloat16
      ~XCiT1d.buffers
      ~XCiT1d.children
      ~XCiT1d.compile
      ~XCiT1d.cpu
      ~XCiT1d.cuda
      ~XCiT1d.double
      ~XCiT1d.eval
      ~XCiT1d.extra_repr
      ~XCiT1d.float
      ~XCiT1d.forward
      ~XCiT1d.get_buffer
      ~XCiT1d.get_extra_state
      ~XCiT1d.get_parameter
      ~XCiT1d.get_submodule
      ~XCiT1d.half
      ~XCiT1d.ipu
      ~XCiT1d.load_state_dict
      ~XCiT1d.modules
      ~XCiT1d.mtia
      ~XCiT1d.named_buffers
      ~XCiT1d.named_children
      ~XCiT1d.named_modules
      ~XCiT1d.named_parameters
      ~XCiT1d.parameters
      ~XCiT1d.register_backward_hook
      ~XCiT1d.register_buffer
      ~XCiT1d.register_forward_hook
      ~XCiT1d.register_forward_pre_hook
      ~XCiT1d.register_full_backward_hook
      ~XCiT1d.register_full_backward_pre_hook
      ~XCiT1d.register_load_state_dict_post_hook
      ~XCiT1d.register_load_state_dict_pre_hook
      ~XCiT1d.register_module
      ~XCiT1d.register_parameter
      ~XCiT1d.register_state_dict_post_hook
      ~XCiT1d.register_state_dict_pre_hook
      ~XCiT1d.requires_grad_
      ~XCiT1d.set_extra_state
      ~XCiT1d.set_submodule
      ~XCiT1d.share_memory
      ~XCiT1d.state_dict
      ~XCiT1d.to
      ~XCiT1d.to_empty
      ~XCiT1d.train
      ~XCiT1d.type
      ~XCiT1d.xpu
      ~XCiT1d.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~XCiT1d.T_destination
      ~XCiT1d.call_super_init
      ~XCiT1d.dump_patches
      ~XCiT1d.training
   
   