torchsig.utils.dsp
==================

.. automodule:: torchsig.utils.dsp

   
   
   

   
   
   .. rubric:: Functions

   .. autosummary::
      :toctree:
      :nosignatures:
   
      bandwidth_from_lower_upper_freq
      center_freq_from_lower_upper_freq
      compute_spectrogram
      convolve
      design_half_band_filter
      estimate_filter_length
      estimate_tone_bandwidth
      frequency_shift
      gaussian_taps
      interpolate_power_of_2_resampler
      is_even
      is_multiple_of_4
      is_odd
      low_pass
      low_pass_iterative_design
      lower_freq_from_center_freq_bandwidth
      multistage_polyphase_decimator
      multistage_polyphase_interpolator
      multistage_polyphase_resampler
      pad_head_tail_to_length
      polyphase_decimator
      polyphase_fractional_resampler
      polyphase_integer_interpolator
      prototype_polyphase_filter
      prototype_polyphase_filter_decimation
      prototype_polyphase_filter_interpolation
      slice_head_tail_to_length
      slice_tail_to_length
      srrc_taps
      upconversion_anti_aliasing_filter
      upper_freq_from_center_freq_bandwidth
      upsample
   
   

   
   
   

   
   
   



