# Copyright 2011,2012,2016,2018,2019 Free Software Foundation, Inc.
#
# This file was generated by gr_modtool, a tool from the GNU Radio framework
# This file is a part of gr-spectrumDetect
#
# SPDX-License-Identifier: GPL-3.0-or-later
#

########################################################################
# Setup library
########################################################################
include(GrPlatform) #define LIB_SUFFIX

list(APPEND spectrumDetect_sources)

set(spectrumDetect_sources
    "${spectrumDetect_sources}"
    PARENT_SCOPE)
if(NOT spectrumDetect_sources)
    message(STATUS "No C++ sources... skipping lib/")
    return()
endif(NOT spectrumDetect_sources)

add_library(gnuradio-spectrumDetect SHARED ${spectrumDetect_sources})
target_link_libraries(gnuradio-spectrumDetect gnuradio::gnuradio-runtime)
target_include_directories(
    gnuradio-spectrumDetect
    PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
    PUBLIC $<INSTALL_INTERFACE:include>)
set_target_properties(gnuradio-spectrumDetect PROPERTIES DEFINE_SYMBOL "gnuradio_spectrumDetect_EXPORTS")

if(APPLE)
    set_target_properties(gnuradio-spectrumDetect PROPERTIES INSTALL_NAME_DIR
                                                    "${CMAKE_INSTALL_PREFIX}/lib")
endif(APPLE)

########################################################################
# Install built library files
########################################################################
include(GrMiscUtils)
gr_library_foo(gnuradio-spectrumDetect)

########################################################################
# Print summary
########################################################################
message(STATUS "Using install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "Building for version: ${VERSION} / ${LIBVER}")

########################################################################
# Build and register unit test
########################################################################
include(GrTest)

# If your unit tests require special include paths, add them here
#include_directories()
# List all files that contain Boost.UTF unit tests here
list(APPEND test_spectrumDetect_sources)
# Anything we need to link to for the unit tests go here
list(APPEND GR_TEST_TARGET_DEPS gnuradio-spectrumDetect)

if(NOT test_spectrumDetect_sources)
    message(STATUS "No C++ unit tests... skipping")
    return()
endif(NOT test_spectrumDetect_sources)

foreach(qa_file ${test_spectrumDetect_sources})
    gr_add_cpp_test("spectrumDetect_${qa_file}" ${CMAKE_CURRENT_SOURCE_DIR}/${qa_file})
endforeach(qa_file)
