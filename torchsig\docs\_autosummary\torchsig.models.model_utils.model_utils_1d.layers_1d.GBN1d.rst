torchsig.models.model\_utils.model\_utils\_1d.layers\_1d.GBN1d
==============================================================

.. currentmodule:: torchsig.models.model_utils.model_utils_1d.layers_1d

.. autoclass:: GBN1d
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~GBN1d.add_module
      ~GBN1d.apply
      ~GBN1d.bfloat16
      ~GBN1d.buffers
      ~GBN1d.children
      ~GBN1d.compile
      ~GBN1d.cpu
      ~GBN1d.cuda
      ~GBN1d.double
      ~GBN1d.eval
      ~GBN1d.extra_repr
      ~GBN1d.float
      ~GBN1d.forward
      ~GBN1d.get_buffer
      ~GBN1d.get_extra_state
      ~GBN1d.get_parameter
      ~GBN1d.get_submodule
      ~GBN1d.half
      ~GBN1d.ipu
      ~GBN1d.load_state_dict
      ~GBN1d.modules
      ~GBN1d.mtia
      ~GBN1d.named_buffers
      ~GBN1d.named_children
      ~GBN1d.named_modules
      ~GBN1d.named_parameters
      ~GBN1d.parameters
      ~GBN1d.register_backward_hook
      ~GBN1d.register_buffer
      ~GBN1d.register_forward_hook
      ~GBN1d.register_forward_pre_hook
      ~GBN1d.register_full_backward_hook
      ~GBN1d.register_full_backward_pre_hook
      ~GBN1d.register_load_state_dict_post_hook
      ~GBN1d.register_load_state_dict_pre_hook
      ~GBN1d.register_module
      ~GBN1d.register_parameter
      ~GBN1d.register_state_dict_post_hook
      ~GBN1d.register_state_dict_pre_hook
      ~GBN1d.requires_grad_
      ~GBN1d.set_extra_state
      ~GBN1d.set_submodule
      ~GBN1d.share_memory
      ~GBN1d.state_dict
      ~GBN1d.to
      ~GBN1d.to_empty
      ~GBN1d.train
      ~GBN1d.type
      ~GBN1d.xpu
      ~GBN1d.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~GBN1d.T_destination
      ~GBN1d.call_super_init
      ~GBN1d.dump_patches
      ~GBN1d.training
   
   