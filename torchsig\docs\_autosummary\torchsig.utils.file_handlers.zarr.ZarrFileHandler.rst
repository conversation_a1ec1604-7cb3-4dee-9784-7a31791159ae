torchsig.utils.file\_handlers.zarr.ZarrFileHandler
==================================================

.. currentmodule:: torchsig.utils.file_handlers.zarr

.. autoclass:: ZarrFileHandler
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~ZarrFileHandler.exists
      ~ZarrFileHandler.load
      ~ZarrFileHandler.setup
      ~ZarrFileHandler.size
      ~ZarrFileHandler.static_load
      ~ZarrFileHandler.teardown
      ~ZarrFileHandler.write
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ZarrFileHandler.chunk_size
      ~ZarrFileHandler.datapath_filename
   
   