torchsig.datasets.dataset\_metadata.NarrowbandMetadata
======================================================

.. currentmodule:: torchsig.datasets.dataset_metadata

.. autoclass:: NarrowbandMetadata
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~NarrowbandMetadata.add_parent
      ~NarrowbandMetadata.get_second_seed
      ~NarrowbandMetadata.seed
      ~NarrowbandMetadata.setup_rngs
      ~NarrowbandMetadata.to_dict
      ~NarrowbandMetadata.update_from_parent
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~NarrowbandMetadata.bandwidth_max
      ~NarrowbandMetadata.bandwidth_min
      ~NarrowbandMetadata.center_freq_max
      ~NarrowbandMetadata.center_freq_min
      ~NarrowbandMetadata.class_distribution
      ~NarrowbandMetadata.class_list
      ~NarrowbandMetadata.dataset_type
      ~NarrowbandMetadata.duration_in_samples_max
      ~NarrowbandMetadata.duration_in_samples_min
      ~NarrowbandMetadata.fft_frequency_max
      ~NarrowbandMetadata.fft_frequency_min
      ~NarrowbandMetadata.fft_frequency_resolution
      ~NarrowbandMetadata.fft_size
      ~NarrowbandMetadata.fft_stride
      ~NarrowbandMetadata.frequency_max
      ~NarrowbandMetadata.frequency_min
      ~NarrowbandMetadata.impairment_level
      ~NarrowbandMetadata.impairments
      ~NarrowbandMetadata.minimum_params
      ~NarrowbandMetadata.noise_power_db
      ~NarrowbandMetadata.num_iq_samples_dataset
      ~NarrowbandMetadata.num_samples
      ~NarrowbandMetadata.num_samples_generated
      ~NarrowbandMetadata.num_signals_distribution
      ~NarrowbandMetadata.num_signals_max
      ~NarrowbandMetadata.num_signals_min
      ~NarrowbandMetadata.num_signals_range
      ~NarrowbandMetadata.sample_rate
      ~NarrowbandMetadata.signal_duration_percent_max
      ~NarrowbandMetadata.signal_duration_percent_min
      ~NarrowbandMetadata.snr_db_max
      ~NarrowbandMetadata.snr_db_min
      ~NarrowbandMetadata.target_transforms
      ~NarrowbandMetadata.transforms
   
   