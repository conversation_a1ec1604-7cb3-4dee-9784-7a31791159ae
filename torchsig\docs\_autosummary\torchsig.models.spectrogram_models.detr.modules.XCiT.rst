torchsig.models.spectrogram\_models.detr.modules.XCiT
=====================================================

.. currentmodule:: torchsig.models.spectrogram_models.detr.modules

.. autoclass:: XCiT
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~XCiT.add_module
      ~XCiT.apply
      ~XCiT.bfloat16
      ~XCiT.buffers
      ~XCiT.children
      ~XCiT.compile
      ~XCiT.cpu
      ~XCiT.cuda
      ~XCiT.double
      ~XCiT.eval
      ~XCiT.extra_repr
      ~XCiT.float
      ~XCiT.forward
      ~XCiT.get_buffer
      ~XCiT.get_extra_state
      ~XCiT.get_parameter
      ~XCiT.get_submodule
      ~XCiT.half
      ~XCiT.ipu
      ~XCiT.load_state_dict
      ~XCiT.modules
      ~XCiT.mtia
      ~XCiT.named_buffers
      ~XCiT.named_children
      ~XCiT.named_modules
      ~XCiT.named_parameters
      ~XCiT.parameters
      ~XCiT.register_backward_hook
      ~XCiT.register_buffer
      ~XCiT.register_forward_hook
      ~XCiT.register_forward_pre_hook
      ~XCiT.register_full_backward_hook
      ~XCiT.register_full_backward_pre_hook
      ~XCiT.register_load_state_dict_post_hook
      ~XCiT.register_load_state_dict_pre_hook
      ~XCiT.register_module
      ~XCiT.register_parameter
      ~XCiT.register_state_dict_post_hook
      ~XCiT.register_state_dict_pre_hook
      ~XCiT.requires_grad_
      ~XCiT.set_extra_state
      ~XCiT.set_submodule
      ~XCiT.share_memory
      ~XCiT.state_dict
      ~XCiT.to
      ~XCiT.to_empty
      ~XCiT.train
      ~XCiT.type
      ~XCiT.xpu
      ~XCiT.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~XCiT.T_destination
      ~XCiT.call_super_init
      ~XCiT.dump_patches
      ~XCiT.training
   
   