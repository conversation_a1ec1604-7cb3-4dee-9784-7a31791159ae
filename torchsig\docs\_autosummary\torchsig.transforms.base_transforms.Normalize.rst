torchsig.transforms.base\_transforms.Normalize
==============================================

.. currentmodule:: torchsig.transforms.base_transforms

.. autoclass:: Normalize
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Normalize.add_parent
      ~Normalize.get_second_seed
      ~Normalize.seed
      ~Normalize.setup_rngs
      ~Normalize.update
      ~Normalize.update_from_parent
   
   

   
   
   