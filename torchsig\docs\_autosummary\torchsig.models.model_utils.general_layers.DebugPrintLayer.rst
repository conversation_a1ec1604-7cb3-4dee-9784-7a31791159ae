torchsig.models.model\_utils.general\_layers.DebugPrintLayer
============================================================

.. currentmodule:: torchsig.models.model_utils.general_layers

.. autoclass:: DebugPrintLayer
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~DebugPrintLayer.add_module
      ~DebugPrintLayer.apply
      ~DebugPrintLayer.bfloat16
      ~DebugPrintLayer.buffers
      ~DebugPrintLayer.children
      ~DebugPrintLayer.compile
      ~DebugPrintLayer.cpu
      ~DebugPrintLayer.cuda
      ~DebugPrintLayer.double
      ~DebugPrintLayer.eval
      ~DebugPrintLayer.extra_repr
      ~DebugPrintLayer.float
      ~DebugPrintLayer.forward
      ~DebugPrintLayer.get_buffer
      ~DebugPrintLayer.get_extra_state
      ~DebugPrintLayer.get_parameter
      ~DebugPrintLayer.get_submodule
      ~DebugPrintLayer.half
      ~DebugPrintLayer.ipu
      ~DebugPrintLayer.load_state_dict
      ~DebugPrintLayer.modules
      ~DebugPrintLayer.mtia
      ~DebugPrintLayer.named_buffers
      ~DebugPrintLayer.named_children
      ~DebugPrintLayer.named_modules
      ~DebugPrintLayer.named_parameters
      ~DebugPrintLayer.parameters
      ~DebugPrintLayer.register_backward_hook
      ~DebugPrintLayer.register_buffer
      ~DebugPrintLayer.register_forward_hook
      ~DebugPrintLayer.register_forward_pre_hook
      ~DebugPrintLayer.register_full_backward_hook
      ~DebugPrintLayer.register_full_backward_pre_hook
      ~DebugPrintLayer.register_load_state_dict_post_hook
      ~DebugPrintLayer.register_load_state_dict_pre_hook
      ~DebugPrintLayer.register_module
      ~DebugPrintLayer.register_parameter
      ~DebugPrintLayer.register_state_dict_post_hook
      ~DebugPrintLayer.register_state_dict_pre_hook
      ~DebugPrintLayer.requires_grad_
      ~DebugPrintLayer.set_extra_state
      ~DebugPrintLayer.set_submodule
      ~DebugPrintLayer.share_memory
      ~DebugPrintLayer.state_dict
      ~DebugPrintLayer.to
      ~DebugPrintLayer.to_empty
      ~DebugPrintLayer.train
      ~DebugPrintLayer.type
      ~DebugPrintLayer.xpu
      ~DebugPrintLayer.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DebugPrintLayer.T_destination
      ~DebugPrintLayer.call_super_init
      ~DebugPrintLayer.dump_patches
      ~DebugPrintLayer.training
   
   