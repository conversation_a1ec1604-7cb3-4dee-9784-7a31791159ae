torchsig.datasets.datamodules.OfficialWidebandDataModule
========================================================

.. currentmodule:: torchsig.datasets.datamodules

.. autoclass:: OfficialWidebandDataModule
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~OfficialWidebandDataModule.from_datasets
      ~OfficialWidebandDataModule.load_from_checkpoint
      ~OfficialWidebandDataModule.load_state_dict
      ~OfficialWidebandDataModule.on_after_batch_transfer
      ~OfficialWidebandDataModule.on_before_batch_transfer
      ~OfficialWidebandDataModule.on_exception
      ~OfficialWidebandDataModule.predict_dataloader
      ~OfficialWidebandDataModule.prepare_data
      ~OfficialWidebandDataModule.save_hyperparameters
      ~OfficialWidebandDataModule.setup
      ~OfficialWidebandDataModule.state_dict
      ~OfficialWidebandDataModule.teardown
      ~OfficialWidebandDataModule.test_dataloader
      ~OfficialWidebandDataModule.train_dataloader
      ~OfficialWidebandDataModule.transfer_batch_to_device
      ~OfficialWidebandDataModule.val_dataloader
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~OfficialWidebandDataModule.CHECKPOINT_HYPER_PARAMS_KEY
      ~OfficialWidebandDataModule.CHECKPOINT_HYPER_PARAMS_NAME
      ~OfficialWidebandDataModule.CHECKPOINT_HYPER_PARAMS_TYPE
      ~OfficialWidebandDataModule.hparams
      ~OfficialWidebandDataModule.hparams_initial
      ~OfficialWidebandDataModule.name
   
   