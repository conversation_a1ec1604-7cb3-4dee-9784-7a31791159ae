torchsig.models.model\_utils.model\_utils\_1d.layers\_1d.ImageFrom1D
====================================================================

.. currentmodule:: torchsig.models.model_utils.model_utils_1d.layers_1d

.. autoclass:: ImageFrom1D
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~ImageFrom1D.add_module
      ~ImageFrom1D.apply
      ~ImageFrom1D.bfloat16
      ~ImageFrom1D.buffers
      ~ImageFrom1D.children
      ~ImageFrom1D.compile
      ~ImageFrom1D.cpu
      ~ImageFrom1D.cuda
      ~ImageFrom1D.double
      ~ImageFrom1D.eval
      ~ImageFrom1D.extra_repr
      ~ImageFrom1D.float
      ~ImageFrom1D.forward
      ~ImageFrom1D.get_buffer
      ~ImageFrom1D.get_extra_state
      ~ImageFrom1D.get_parameter
      ~ImageFrom1D.get_submodule
      ~ImageFrom1D.half
      ~ImageFrom1D.ipu
      ~ImageFrom1D.load_state_dict
      ~ImageFrom1D.modules
      ~ImageFrom1D.mtia
      ~ImageFrom1D.named_buffers
      ~ImageFrom1D.named_children
      ~ImageFrom1D.named_modules
      ~ImageFrom1D.named_parameters
      ~ImageFrom1D.parameters
      ~ImageFrom1D.register_backward_hook
      ~ImageFrom1D.register_buffer
      ~ImageFrom1D.register_forward_hook
      ~ImageFrom1D.register_forward_pre_hook
      ~ImageFrom1D.register_full_backward_hook
      ~ImageFrom1D.register_full_backward_pre_hook
      ~ImageFrom1D.register_load_state_dict_post_hook
      ~ImageFrom1D.register_load_state_dict_pre_hook
      ~ImageFrom1D.register_module
      ~ImageFrom1D.register_parameter
      ~ImageFrom1D.register_state_dict_post_hook
      ~ImageFrom1D.register_state_dict_pre_hook
      ~ImageFrom1D.requires_grad_
      ~ImageFrom1D.set_extra_state
      ~ImageFrom1D.set_submodule
      ~ImageFrom1D.share_memory
      ~ImageFrom1D.state_dict
      ~ImageFrom1D.to
      ~ImageFrom1D.to_empty
      ~ImageFrom1D.train
      ~ImageFrom1D.type
      ~ImageFrom1D.xpu
      ~ImageFrom1D.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ImageFrom1D.T_destination
      ~ImageFrom1D.call_super_init
      ~ImageFrom1D.dump_patches
      ~ImageFrom1D.training
   
   