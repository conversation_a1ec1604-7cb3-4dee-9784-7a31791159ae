torchsig.models.model\_utils.general\_layers.DropChannel
========================================================

.. currentmodule:: torchsig.models.model_utils.general_layers

.. autoclass:: DropChannel
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~DropChannel.add_module
      ~DropChannel.apply
      ~DropChannel.bfloat16
      ~DropChannel.buffers
      ~DropChannel.children
      ~DropChannel.compile
      ~DropChannel.cpu
      ~DropChannel.cuda
      ~DropChannel.double
      ~DropChannel.eval
      ~DropChannel.extra_repr
      ~DropChannel.float
      ~DropChannel.forward
      ~DropChannel.get_buffer
      ~DropChannel.get_extra_state
      ~DropChannel.get_parameter
      ~DropChannel.get_submodule
      ~DropChannel.half
      ~DropChannel.ipu
      ~DropChannel.load_state_dict
      ~DropChannel.modules
      ~DropChannel.mtia
      ~DropChannel.named_buffers
      ~DropChannel.named_children
      ~DropChannel.named_modules
      ~DropChannel.named_parameters
      ~DropChannel.parameters
      ~DropChannel.register_backward_hook
      ~DropChannel.register_buffer
      ~DropChannel.register_forward_hook
      ~DropChannel.register_forward_pre_hook
      ~DropChannel.register_full_backward_hook
      ~DropChannel.register_full_backward_pre_hook
      ~DropChannel.register_load_state_dict_post_hook
      ~DropChannel.register_load_state_dict_pre_hook
      ~DropChannel.register_module
      ~DropChannel.register_parameter
      ~DropChannel.register_state_dict_post_hook
      ~DropChannel.register_state_dict_pre_hook
      ~DropChannel.requires_grad_
      ~DropChannel.set_extra_state
      ~DropChannel.set_submodule
      ~DropChannel.share_memory
      ~DropChannel.state_dict
      ~DropChannel.to
      ~DropChannel.to_empty
      ~DropChannel.train
      ~DropChannel.type
      ~DropChannel.xpu
      ~DropChannel.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DropChannel.T_destination
      ~DropChannel.call_super_init
      ~DropChannel.dump_patches
      ~DropChannel.training
   
   