torchsig.models.spectrogram\_models.detr.criterion
==================================================

.. automodule:: torchsig.models.spectrogram_models.detr.criterion

   
   
   

   
   
   .. rubric:: Functions

   .. autosummary::
      :toctree:
      :nosignatures:
   
      batch_dice_loss
      batch_sigmoid_ce_loss
      calculate_uncertainty
      dice_loss
      get_uncertain_point_coords_with_randomness
      get_world_size
      is_dist_avail_and_initialized
      nested_tensor_from_tensor_list
      point_sample
      sigmoid_ce_loss
   
   

   
   
   .. rubric:: Classes

   .. autosummary::
      :toctree:
      :template: custom_class_template.rst
      :nosignatures:
   
      HungarianMatcher
      NestedTensor
      SetCriterion
   
   

   
   
   



