Target Transforms
======================

.. currentmodule:: torchsig.transforms.target_transforms

Target Transforms are used to alter the output of the datasets which can be certain labels, class names, or bounding box information.
They only read and add the signal metadata, and do not change the signal data.

.. contents:: Target Transforms
    :local:

.. automodule:: torchsig.transforms.target_transforms
    :members:
    :undoc-members:
    :show-inheritance: