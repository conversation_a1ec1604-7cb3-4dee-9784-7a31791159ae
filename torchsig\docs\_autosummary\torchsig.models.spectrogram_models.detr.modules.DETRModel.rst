torchsig.models.spectrogram\_models.detr.modules.DETRModel
==========================================================

.. currentmodule:: torchsig.models.spectrogram_models.detr.modules

.. autoclass:: DETRModel
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~DETRModel.add_module
      ~DETRModel.apply
      ~DETRModel.bfloat16
      ~DETRModel.buffers
      ~DETRModel.children
      ~DETRModel.compile
      ~DETRModel.cpu
      ~DETRModel.cuda
      ~DETRModel.double
      ~DETRModel.eval
      ~DETRModel.extra_repr
      ~DETRModel.float
      ~DETRModel.forward
      ~DETRModel.get_buffer
      ~DETRModel.get_extra_state
      ~DETRModel.get_parameter
      ~DETRModel.get_submodule
      ~DETRModel.half
      ~DETRModel.ipu
      ~DETRModel.load_state_dict
      ~DETRModel.modules
      ~DETRModel.mtia
      ~DETRModel.named_buffers
      ~DETRModel.named_children
      ~DETRModel.named_modules
      ~DETRModel.named_parameters
      ~DETRModel.parameters
      ~DETRModel.register_backward_hook
      ~DETRModel.register_buffer
      ~DETRModel.register_forward_hook
      ~DETRModel.register_forward_pre_hook
      ~DETRModel.register_full_backward_hook
      ~DETRModel.register_full_backward_pre_hook
      ~DETRModel.register_load_state_dict_post_hook
      ~DETRModel.register_load_state_dict_pre_hook
      ~DETRModel.register_module
      ~DETRModel.register_parameter
      ~DETRModel.register_state_dict_post_hook
      ~DETRModel.register_state_dict_pre_hook
      ~DETRModel.requires_grad_
      ~DETRModel.set_extra_state
      ~DETRModel.set_submodule
      ~DETRModel.share_memory
      ~DETRModel.state_dict
      ~DETRModel.to
      ~DETRModel.to_empty
      ~DETRModel.train
      ~DETRModel.type
      ~DETRModel.xpu
      ~DETRModel.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DETRModel.T_destination
      ~DETRModel.call_super_init
      ~DETRModel.dump_patches
      ~DETRModel.training
   
   