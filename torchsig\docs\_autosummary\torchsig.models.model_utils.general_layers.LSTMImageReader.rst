torchsig.models.model\_utils.general\_layers.LSTMImageReader
============================================================

.. currentmodule:: torchsig.models.model_utils.general_layers

.. autoclass:: LSTMImageReader
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~LSTMImageReader.add_module
      ~LSTMImageReader.apply
      ~LSTMImageReader.bfloat16
      ~LSTMImageReader.buffers
      ~LSTMImageReader.children
      ~LSTMImageReader.compile
      ~LSTMImageReader.cpu
      ~LSTMImageReader.cuda
      ~LSTMImageReader.double
      ~LSTMImageReader.eval
      ~LSTMImageReader.extra_repr
      ~LSTMImageReader.float
      ~LSTMImageReader.forward
      ~LSTMImageReader.get_buffer
      ~LSTMImageReader.get_extra_state
      ~LSTMImageReader.get_parameter
      ~LSTMImageReader.get_submodule
      ~LSTMImageReader.half
      ~LSTMImageReader.ipu
      ~LSTMImageReader.load_state_dict
      ~LSTMImageReader.modules
      ~LSTMImageReader.mtia
      ~LSTMImageReader.named_buffers
      ~LSTMImageReader.named_children
      ~LSTMImageReader.named_modules
      ~LSTMImageReader.named_parameters
      ~LSTMImageReader.parameters
      ~LSTMImageReader.register_backward_hook
      ~LSTMImageReader.register_buffer
      ~LSTMImageReader.register_forward_hook
      ~LSTMImageReader.register_forward_pre_hook
      ~LSTMImageReader.register_full_backward_hook
      ~LSTMImageReader.register_full_backward_pre_hook
      ~LSTMImageReader.register_load_state_dict_post_hook
      ~LSTMImageReader.register_load_state_dict_pre_hook
      ~LSTMImageReader.register_module
      ~LSTMImageReader.register_parameter
      ~LSTMImageReader.register_state_dict_post_hook
      ~LSTMImageReader.register_state_dict_pre_hook
      ~LSTMImageReader.requires_grad_
      ~LSTMImageReader.set_extra_state
      ~LSTMImageReader.set_submodule
      ~LSTMImageReader.share_memory
      ~LSTMImageReader.state_dict
      ~LSTMImageReader.to
      ~LSTMImageReader.to_empty
      ~LSTMImageReader.train
      ~LSTMImageReader.type
      ~LSTMImageReader.xpu
      ~LSTMImageReader.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~LSTMImageReader.T_destination
      ~LSTMImageReader.call_super_init
      ~LSTMImageReader.dump_patches
      ~LSTMImageReader.training
   
   