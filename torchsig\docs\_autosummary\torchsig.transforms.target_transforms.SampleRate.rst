torchsig.transforms.target\_transforms.SampleRate
=================================================

.. currentmodule:: torchsig.transforms.target_transforms

.. autoclass:: SampleRate
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~SampleRate.add_parent
      ~SampleRate.get_second_seed
      ~SampleRate.seed
      ~SampleRate.setup_rngs
      ~SampleRate.update
      ~SampleRate.update_from_parent
   
   

   
   
   