torchsig.models.model\_utils.general\_layers.Mean
=================================================

.. currentmodule:: torchsig.models.model_utils.general_layers

.. autoclass:: Mean
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Mean.add_module
      ~Mean.apply
      ~Mean.bfloat16
      ~Mean.buffers
      ~Mean.children
      ~Mean.compile
      ~Mean.cpu
      ~Mean.cuda
      ~Mean.double
      ~Mean.eval
      ~Mean.extra_repr
      ~Mean.float
      ~Mean.forward
      ~Mean.get_buffer
      ~Mean.get_extra_state
      ~Mean.get_parameter
      ~Mean.get_submodule
      ~Mean.half
      ~Mean.ipu
      ~Mean.load_state_dict
      ~Mean.modules
      ~Mean.mtia
      ~Mean.named_buffers
      ~Mean.named_children
      ~Mean.named_modules
      ~Mean.named_parameters
      ~Mean.parameters
      ~Mean.register_backward_hook
      ~Mean.register_buffer
      ~Mean.register_forward_hook
      ~Mean.register_forward_pre_hook
      ~Mean.register_full_backward_hook
      ~Mean.register_full_backward_pre_hook
      ~Mean.register_load_state_dict_post_hook
      ~Mean.register_load_state_dict_pre_hook
      ~Mean.register_module
      ~Mean.register_parameter
      ~Mean.register_state_dict_post_hook
      ~Mean.register_state_dict_pre_hook
      ~Mean.requires_grad_
      ~Mean.set_extra_state
      ~Mean.set_submodule
      ~Mean.share_memory
      ~Mean.state_dict
      ~Mean.to
      ~Mean.to_empty
      ~Mean.train
      ~Mean.type
      ~Mean.xpu
      ~Mean.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~Mean.T_destination
      ~Mean.call_super_init
      ~Mean.dump_patches
      ~Mean.training
   
   