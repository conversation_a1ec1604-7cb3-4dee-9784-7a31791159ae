torchsig.datasets.datamodules.OfficialTorchSigDataModdule
=========================================================

.. currentmodule:: torchsig.datasets.datamodules

.. autoclass:: OfficialTorchSigDataModdule
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~OfficialTorchSigDataModdule.from_datasets
      ~OfficialTorchSigDataModdule.load_from_checkpoint
      ~OfficialTorchSigDataModdule.load_state_dict
      ~OfficialTorchSigDataModdule.on_after_batch_transfer
      ~OfficialTorchSigDataModdule.on_before_batch_transfer
      ~OfficialTorchSigDataModdule.on_exception
      ~OfficialTorchSigDataModdule.predict_dataloader
      ~OfficialTorchSigDataModdule.prepare_data
      ~OfficialTorchSigDataModdule.save_hyperparameters
      ~OfficialTorchSigDataModdule.setup
      ~OfficialTorchSigDataModdule.state_dict
      ~OfficialTorchSigDataModdule.teardown
      ~OfficialTorchSigDataModdule.test_dataloader
      ~OfficialTorchSigDataModdule.train_dataloader
      ~OfficialTorchSigDataModdule.transfer_batch_to_device
      ~OfficialTorchSigDataModdule.val_dataloader
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~OfficialTorchSigDataModdule.CHECKPOINT_HYPER_PARAMS_KEY
      ~OfficialTorchSigDataModdule.CHECKPOINT_HYPER_PARAMS_NAME
      ~OfficialTorchSigDataModdule.CHECKPOINT_HYPER_PARAMS_TYPE
      ~OfficialTorchSigDataModdule.hparams
      ~OfficialTorchSigDataModdule.hparams_initial
      ~OfficialTorchSigDataModdule.name
   
   