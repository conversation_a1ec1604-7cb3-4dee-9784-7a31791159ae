torchsig.datasets.dataset\_metadata.DatasetMetadata
===================================================

.. currentmodule:: torchsig.datasets.dataset_metadata

.. autoclass:: DatasetMetadata
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~DatasetMetadata.add_parent
      ~DatasetMetadata.get_second_seed
      ~DatasetMetadata.seed
      ~DatasetMetadata.setup_rngs
      ~DatasetMetadata.to_dict
      ~DatasetMetadata.update_from_parent
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DatasetMetadata.bandwidth_max
      ~DatasetMetadata.bandwidth_min
      ~DatasetMetadata.center_freq_max
      ~DatasetMetadata.center_freq_min
      ~DatasetMetadata.class_distribution
      ~DatasetMetadata.class_list
      ~DatasetMetadata.dataset_type
      ~DatasetMetadata.duration_in_samples_max
      ~DatasetMetadata.duration_in_samples_min
      ~DatasetMetadata.fft_frequency_max
      ~DatasetMetadata.fft_frequency_min
      ~DatasetMetadata.fft_frequency_resolution
      ~DatasetMetadata.fft_size
      ~DatasetMetadata.fft_stride
      ~DatasetMetadata.frequency_max
      ~DatasetMetadata.frequency_min
      ~DatasetMetadata.impairment_level
      ~DatasetMetadata.impairments
      ~DatasetMetadata.minimum_params
      ~DatasetMetadata.noise_power_db
      ~DatasetMetadata.num_iq_samples_dataset
      ~DatasetMetadata.num_samples
      ~DatasetMetadata.num_samples_generated
      ~DatasetMetadata.num_signals_distribution
      ~DatasetMetadata.num_signals_max
      ~DatasetMetadata.num_signals_min
      ~DatasetMetadata.num_signals_range
      ~DatasetMetadata.sample_rate
      ~DatasetMetadata.signal_duration_percent_max
      ~DatasetMetadata.signal_duration_percent_min
      ~DatasetMetadata.snr_db_max
      ~DatasetMetadata.snr_db_min
      ~DatasetMetadata.target_transforms
      ~DatasetMetadata.transforms
   
   