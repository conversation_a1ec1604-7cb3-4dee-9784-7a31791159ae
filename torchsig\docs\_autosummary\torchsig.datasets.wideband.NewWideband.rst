torchsig.datasets.wideband.NewWideband
======================================

.. currentmodule:: torchsig.datasets.wideband

.. autoclass:: NewWideband
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~NewWideband.add_parent
      ~NewWideband.get_second_seed
      ~NewWideband.reset
      ~NewWideband.seed
      ~NewWideband.setup_rngs
      ~NewWideband.update_from_parent
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~NewWideband.dataset_metadata
   
   