#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
独立的预检查脚本
在生成大型数据集之前验证所有调制方式和SNR设置
"""

import sys
import argparse
from generate_hisar_dataset import pre_generation_check, parse_args

def main():
    """独立预检查主函数"""
    print("🔍 TorchSig数据生成预检查工具")
    print("=" * 50)
    print("此工具将验证所有调制方式和SNR设置是否正常工作")
    print("建议在生成大型数据集之前运行此检查")
    print("=" * 50)
    
    # 解析命令行参数（复用主脚本的参数）
    args = parse_args()
    
    # 显示检查配置
    print(f"\n📋 检查配置:")
    print(f"   SNR范围: {args.snr_min}dB 到 {args.snr_max}dB")
    print(f"   SNR步长: {args.snr_step}dB")
    print(f"   序列长度: {args.sequence_length}")
    print(f"   生成方式: {'直接生成' if args.direct_generation else '数据集API'}")
    print(f"   随机种子: {args.seed}")
    
    # 执行预检查
    try:
        check_passed = pre_generation_check(args)
        
        if check_passed:
            print(f"\n🎉 恭喜！预检查完全通过")
            print(f"✅ 现在可以安全地运行完整的数据集生成")
            print(f"\n💡 运行完整生成的命令:")
            print(f"python generate_hisar_dataset.py --skip_precheck")
            sys.exit(0)
        else:
            print(f"\n⚠️  预检查发现问题")
            print(f"❌ 建议修复问题后再生成大型数据集")
            print(f"\n💡 如果要强制继续，可以运行:")
            print(f"python generate_hisar_dataset.py")
            print(f"（系统会询问是否继续）")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️  用户中断了预检查")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 预检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
