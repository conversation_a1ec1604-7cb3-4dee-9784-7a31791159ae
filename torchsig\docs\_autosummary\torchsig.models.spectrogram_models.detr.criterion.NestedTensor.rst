torchsig.models.spectrogram\_models.detr.criterion.NestedTensor
===============================================================

.. currentmodule:: torchsig.models.spectrogram_models.detr.criterion

.. autoclass:: NestedTensor
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~NestedTensor.decompose
      ~NestedTensor.to
   
   

   
   
   