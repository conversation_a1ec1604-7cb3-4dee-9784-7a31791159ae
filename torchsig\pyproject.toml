[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "torchsig"
description = "Signal Processing Machine Learning Toolkit"
authors = [
    {name = "TorchSig Team"},
]
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
]
keywords = ["signal processing", "machine learning"]
dependencies = [
    "torch",
    "torchvision",
    "tqdm",
    "opencv-python",
    "numpy",
    "scipy",
    "h5py",
    "matplotlib",
    "PyWavelets",
    "pandas",
    "scikit-learn",
    "torchaudio",
    "timm",
    "pytorch_lightning",
    "ultralytics",
    "jupyter_bbox_widget",
    "torchinfo",
    "sigmf",
    "pytest",
    "zarr==2.18.3",
    "pylint",
    "pytest-cov",
]
dynamic = ["version"]

[tool.setuptools.dynamic]
version = {attr = "torchsig.__version__"}


[tool.coverage.run]
branch = true
source = ["torchsig"]

[tool.coverage.report]
show_missing = true
skip_covered = true
omit = [
    "torchsig/image_datasets/*",
    "torchsig/models/*"
]

[tool.pytest.ini_options]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
]
addopts = "-m 'not slow' --cov=torchsig --cov-report=xml --cov-report=term --junitxml=report.xml"
testpaths = ["tests"]
