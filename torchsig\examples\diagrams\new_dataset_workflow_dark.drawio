<mxfile host="65bd71144e">
    <diagram id="laA0arsnX7Pyb7320zwb" name="Page-1">
        <mxGraphModel dx="1090" dy="675" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" background="#000000" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="34" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Source+Code+Pro&quot; face=&quot;Source Code Pro&quot;&gt;self.__generate_new_signal__()&lt;/font&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=bottom;labelPosition=center;verticalLabelPosition=top;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="40" y="110" width="770" height="350" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="&lt;font color=&quot;#ffffff&quot;&gt;NewDataset __getitem__ Workflow&lt;/font&gt;" style="text;html=1;strokeColor=#FFFFFF;fillColor=#000000;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=36;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="20" y="10" width="820" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="52" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=9;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="10" target="21" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;Target Transforms: produce labels requested from user&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; background-color: initial; font-family: &amp;quot;Source Code Pro&amp;quot;;&quot;&gt;targets = self.dataset_metadata.target_transforms(sample)&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="44.370000000000005" y="810" width="351.25" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="39" style="edgeStyle=none;html=1;fontSize=9;exitX=0.538;exitY=1.018;exitDx=0;exitDy=0;exitPerimeter=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;entryX=0.766;entryY=0.004;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="12" target="25" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="740" y="270" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="apply" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="39" vertex="1" connectable="0">
                    <mxGeometry x="-0.1959" y="-2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="&lt;font color=&quot;#ffffff&quot;&gt;Signal&lt;/font&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="660" y="160" width="135" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="&lt;font color=&quot;#ffffff&quot;&gt;data: np.ndarray&lt;br&gt;metadata: SignalMetadata&lt;/font&gt;" style="text;strokeColor=#FFFFFF;fillColor=#000000;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=12;shadow=0;" parent="11" vertex="1">
                    <mxGeometry y="30" width="135" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Source+Code+Pro&quot; face=&quot;Source Code Pro&quot;&gt;return sample.data, targets&lt;/font&gt;&lt;/span&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="469.07" y="820" width="220" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="50" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=9;exitX=0.5;exitY=1.017;exitDx=0;exitDy=0;exitPerimeter=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="23" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="190.62" y="750" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="apply" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="50" vertex="1" connectable="0">
                    <mxGeometry x="-0.4173" relative="1" as="geometry">
                        <mxPoint y="9" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="DatasetDict" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="130" y="660" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="data: np.ndarray&lt;br&gt;metadata: [dict]" style="text;strokeColor=#FFFFFF;fillColor=#000000;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=12;shadow=0;fontColor=#FFFFFF;" parent="22" vertex="1">
                    <mxGeometry y="30" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="40" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=9;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="53" target="31" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="create" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="40" vertex="1" connectable="0">
                    <mxGeometry x="-0.2149" y="2" relative="1" as="geometry">
                        <mxPoint x="-2" y="-3" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" style="edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontFamily=Source Code Pro;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DSource%2BCode%2BPro;fontSize=10;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="1" source="25" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;Signal Transforms: applied to isolated signals&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;background-color: initial; font-family: &amp;quot;Source Code Pro&amp;quot;; font-size: 10px;&quot;&gt;new_signal = self.impairments(new_signal)&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="530" y="310" width="265" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="48" style="edgeStyle=none;html=1;fontSize=9;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1.004;entryY=0.194;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="30" target="23" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="400.62" y="710" as="sourcePoint"/>
                        <mxPoint x="290.62" y="710" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="convert to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="48" vertex="1" connectable="0">
                    <mxGeometry x="-0.2842" y="-1" relative="1" as="geometry">
                        <mxPoint x="-21" y="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;User Transforms: defined by user, applied to whole IQ cut, includes ML transforms&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; font-family: &amp;quot;Source Code Pro&amp;quot;; background-color: initial;&quot;&gt;sample = self.dataset_metadata.transforms(sample)&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="420" y="660" width="318.13" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="42" style="edgeStyle=none;html=1;fontSize=9;exitX=0.999;exitY=0.189;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="32" target="56" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="330.62" y="565" as="targetPoint"/>
                        <mxPoint x="271.12" y="549.9000000000001" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" value="apply" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="42" vertex="1" connectable="0">
                    <mxGeometry x="-0.3046" relative="1" as="geometry">
                        <mxPoint x="17" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="DatasetSignal" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="130" y="510" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="data: np.ndarray&lt;br&gt;metadata: [SignalMetadata]" style="text;strokeColor=#FFFFFF;fillColor=#000000;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=12;shadow=0;fontColor=#FFFFFF;" parent="31" vertex="1">
                    <mxGeometry y="30" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="38" style="edgeStyle=none;html=1;entryX=0.001;entryY=-0.026;entryDx=0;entryDy=0;fontSize=9;entryPerimeter=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="16" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="returns" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="38" vertex="1" connectable="0">
                    <mxGeometry x="-0.2329" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="&lt;font color=&quot;#ffffff&quot;&gt;SignalBuilder&lt;/font&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="380" y="130" width="220" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="&lt;font color=&quot;#ffffff&quot;&gt;Modulator -&amp;gt; clean basebanded signal&lt;br&gt;&lt;br&gt;&lt;font style=&quot;font-size: 10px;&quot; data-font-src=&quot;https://fonts.googleapis.com/css?family=Source+Code+Pro&quot; face=&quot;Source Code Pro&quot;&gt;builder.build()&lt;/font&gt;&lt;/font&gt;" style="text;strokeColor=#FFFFFF;fillColor=#000000;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=12;shadow=0;" parent="16" vertex="1">
                    <mxGeometry y="30" width="220" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="37" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=9;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="33" target="16" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="choose" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="37" vertex="1" connectable="0">
                    <mxGeometry x="0.0487" y="2" relative="1" as="geometry">
                        <mxPoint x="-7" y="2" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;Choose random signal to generate&lt;/font&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;background-color: initial; font-size: 10px; font-family: &amp;quot;Source Code Pro&amp;quot;;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;class_name = self._random_signal_class()&lt;/font&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; font-family: &amp;quot;Source Code Pro&amp;quot;; background-color: initial;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;builder = self.builders[class_name]&lt;/font&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;strokeColor=#FFFFFF;fillColor=#000000;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="50" y="127.5" width="270" height="125" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;Place on narrowband/wideband cut&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; background-color: initial; font-family: &amp;quot;Source Code Pro&amp;quot;;&quot;&gt;iq_samples[start:stop] += new_signal.data&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="90" y="310" width="260" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;Dataset Transforms: applied to whole IQ cut&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; font-family: &amp;quot;Source Code Pro&amp;quot;; background-color: initial;&quot;&gt;sample = self.impairment_transforms(sample)&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="434.06" y="510" width="290" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="58" style="edgeStyle=none;html=1;fontSize=9;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;" parent="1" source="56" target="30" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="360.62" y="560" as="targetPoint"/>
                        <mxPoint x="285.43999999999994" y="559.45" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="apply" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;fillColor=#000000;strokeColor=#FFFFFF;shadow=0;fontColor=#FFFFFF;labelBackgroundColor=#000000;" parent="58" vertex="1" connectable="0">
                    <mxGeometry x="-0.3046" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>