id: spectrumDetect_spectrumPlot
label: spectrumPlot
category: '[spectrumDetect]'

templates:
  imports: |-
        from gnuradio import spectrumDetect
        from gnuradio import qtgui
  var_make: self.${id} = ${id} = ${value}
  make: |-
        <% 
           win = 'self._%s_win'%id
        %>\
        ${win} = spectrumDetect.spectrumPlot(${save},${(label if (len(label) - 2 > 0) else repr(id))})
        self.${id} = ${win}

        ${gui_hint() % win}



parameters:
-   id: save
    label: saveSpectrum
    dtype: bool
    default: False
-   id: label
    label: Label
    dtype: string
    hide: ${ ('none' if label else 'part') }
-   id: gui_hint
    label: GUI Hint
    dtype: gui_hint
    hide: part


inputs:
- label: detect_pmt
  domain: message
  dtype: pmt
  vlen: 1

file_format: 1
