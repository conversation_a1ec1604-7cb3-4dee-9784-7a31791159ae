torchsig.transforms.base\_transforms.Transform
==============================================

.. currentmodule:: torchsig.transforms.base_transforms

.. autoclass:: Transform
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Transform.add_parent
      ~Transform.get_second_seed
      ~Transform.seed
      ~Transform.setup_rngs
      ~Transform.update
      ~Transform.update_from_parent
   
   

   
   
   