torchsig.models.spectrogram\_models.detr.modules.MLP
====================================================

.. currentmodule:: torchsig.models.spectrogram_models.detr.modules

.. autoclass:: MLP
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~MLP.add_module
      ~MLP.apply
      ~MLP.bfloat16
      ~MLP.buffers
      ~MLP.children
      ~MLP.compile
      ~MLP.cpu
      ~MLP.cuda
      ~MLP.double
      ~MLP.eval
      ~MLP.extra_repr
      ~MLP.float
      ~MLP.forward
      ~MLP.get_buffer
      ~MLP.get_extra_state
      ~MLP.get_parameter
      ~MLP.get_submodule
      ~MLP.half
      ~MLP.ipu
      ~MLP.load_state_dict
      ~MLP.modules
      ~MLP.mtia
      ~MLP.named_buffers
      ~MLP.named_children
      ~MLP.named_modules
      ~MLP.named_parameters
      ~MLP.parameters
      ~MLP.register_backward_hook
      ~MLP.register_buffer
      ~MLP.register_forward_hook
      ~MLP.register_forward_pre_hook
      ~MLP.register_full_backward_hook
      ~MLP.register_full_backward_pre_hook
      ~MLP.register_load_state_dict_post_hook
      ~MLP.register_load_state_dict_pre_hook
      ~MLP.register_module
      ~MLP.register_parameter
      ~MLP.register_state_dict_post_hook
      ~MLP.register_state_dict_pre_hook
      ~MLP.requires_grad_
      ~MLP.set_extra_state
      ~MLP.set_submodule
      ~MLP.share_memory
      ~MLP.state_dict
      ~MLP.to
      ~MLP.to_empty
      ~MLP.train
      ~MLP.type
      ~MLP.xpu
      ~MLP.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~MLP.T_destination
      ~MLP.call_super_init
      ~MLP.dump_patches
      ~MLP.training
   
   