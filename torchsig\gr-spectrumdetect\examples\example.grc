options:
  parameters:
    author: ''
    catch_exceptions: 'True'
    category: '[GRC Hier Blocks]'
    cmake_opt: ''
    comment: ''
    copyright: ''
    description: ''
    gen_cmake: 'On'
    gen_linking: dynamic
    generate_options: qt_gui
    hier_block_src_path: '.:'
    id: example
    max_nouts: '0'
    output_language: python
    placement: (0,0)
    qt_qss_theme: ''
    realtime_scheduling: ''
    run: 'True'
    run_command: '{python} -u {filename}'
    run_options: prompt
    sizing_mode: fixed
    thread_safe_setters: ''
    title: Not titled yet
    window_size: (1000,1000)
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [8, 8]
    rotation: 0
    state: enabled

blocks:
- name: IP_INFO
  id: variable
  parameters:
    comment: ''
    value: '"addr=************"'
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [712, 12.0]
    rotation: 0
    state: enabled
- name: centerFrequency
  id: variable
  parameters:
    comment: ''
    value: '2442000000'
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [288, 12.0]
    rotation: 0
    state: enabled
- name: nfft
  id: variable
  parameters:
    comment: ''
    value: '1024'
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [408, 12.0]
    rotation: 0
    state: enabled
- name: sampleRate
  id: variable
  parameters:
    comment: ''
    value: '25000000'
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [184, 12]
    rotation: 0
    state: enabled
- name: trainedModel
  id: variable
  parameters:
    comment: ''
    value: '"detect.pt"'
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [608, 12.0]
    rotation: 0
    state: enabled
- name: vectorSize
  id: variable
  parameters:
    comment: ''
    value: int(nfft*nfft)
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [504, 12.0]
    rotation: 0
    state: enabled
- name: blocks_stream_to_vector_0
  id: blocks_stream_to_vector
  parameters:
    affinity: ''
    alias: ''
    comment: ''
    maxoutbuf: '0'
    minoutbuf: sampleRate//vectorSize
    num_items: vectorSize
    type: complex
    vlen: '1'
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [376, 224.0]
    rotation: 0
    state: enabled
- name: spectrumDetect_specDetect_1
  id: spectrumDetect_specDetect
  parameters:
    affinity: ''
    alias: ''
    augment: 'False'
    center_frequency: centerFrequency
    comment: ''
    debug_sigMF: 'False'
    iou: '0.1'
    max_det: '300'
    maxoutbuf: '0'
    minoutbuf: '0'
    n_fft: nfft
    sample_rate: sampleRate
    save: 'False'
    trained_model: trainedModel
    vector_size: vectorSize
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [616, 156.0]
    rotation: 0
    state: enabled
- name: spectrumDetect_spectrumPlot_0
  id: spectrumDetect_spectrumPlot
  parameters:
    affinity: ''
    alias: ''
    comment: ''
    gui_hint: ''
    label: ''
    save: 'False'
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [960, 220.0]
    rotation: 0
    state: enabled
- name: uhd_usrp_source_0
  id: uhd_usrp_source
  parameters:
    affinity: ''
    alias: ''
    ant0: '"TX/RX"'
    ant1: '"RX2"'
    ant10: '"RX2"'
    ant11: '"RX2"'
    ant12: '"RX2"'
    ant13: '"RX2"'
    ant14: '"RX2"'
    ant15: '"RX2"'
    ant16: '"RX2"'
    ant17: '"RX2"'
    ant18: '"RX2"'
    ant19: '"RX2"'
    ant2: '"RX2"'
    ant20: '"RX2"'
    ant21: '"RX2"'
    ant22: '"RX2"'
    ant23: '"RX2"'
    ant24: '"RX2"'
    ant25: '"RX2"'
    ant26: '"RX2"'
    ant27: '"RX2"'
    ant28: '"RX2"'
    ant29: '"RX2"'
    ant3: '"RX2"'
    ant30: '"RX2"'
    ant31: '"RX2"'
    ant4: '"RX2"'
    ant5: '"RX2"'
    ant6: '"RX2"'
    ant7: '"RX2"'
    ant8: '"RX2"'
    ant9: '"RX2"'
    bw0: '0'
    bw1: '0'
    bw10: '0'
    bw11: '0'
    bw12: '0'
    bw13: '0'
    bw14: '0'
    bw15: '0'
    bw16: '0'
    bw17: '0'
    bw18: '0'
    bw19: '0'
    bw2: '0'
    bw20: '0'
    bw21: '0'
    bw22: '0'
    bw23: '0'
    bw24: '0'
    bw25: '0'
    bw26: '0'
    bw27: '0'
    bw28: '0'
    bw29: '0'
    bw3: '0'
    bw30: '0'
    bw31: '0'
    bw4: '0'
    bw5: '0'
    bw6: '0'
    bw7: '0'
    bw8: '0'
    bw9: '0'
    center_freq0: centerFrequency
    center_freq1: '0'
    center_freq10: '0'
    center_freq11: '0'
    center_freq12: '0'
    center_freq13: '0'
    center_freq14: '0'
    center_freq15: '0'
    center_freq16: '0'
    center_freq17: '0'
    center_freq18: '0'
    center_freq19: '0'
    center_freq2: '0'
    center_freq20: '0'
    center_freq21: '0'
    center_freq22: '0'
    center_freq23: '0'
    center_freq24: '0'
    center_freq25: '0'
    center_freq26: '0'
    center_freq27: '0'
    center_freq28: '0'
    center_freq29: '0'
    center_freq3: '0'
    center_freq30: '0'
    center_freq31: '0'
    center_freq4: '0'
    center_freq5: '0'
    center_freq6: '0'
    center_freq7: '0'
    center_freq8: '0'
    center_freq9: '0'
    clock_rate: 200e6
    clock_source0: ''
    clock_source1: ''
    clock_source2: ''
    clock_source3: ''
    clock_source4: ''
    clock_source5: ''
    clock_source6: ''
    clock_source7: ''
    comment: ''
    dc_offs0: 0+0j
    dc_offs1: 0+0j
    dc_offs10: 0+0j
    dc_offs11: 0+0j
    dc_offs12: 0+0j
    dc_offs13: 0+0j
    dc_offs14: 0+0j
    dc_offs15: 0+0j
    dc_offs16: 0+0j
    dc_offs17: 0+0j
    dc_offs18: 0+0j
    dc_offs19: 0+0j
    dc_offs2: 0+0j
    dc_offs20: 0+0j
    dc_offs21: 0+0j
    dc_offs22: 0+0j
    dc_offs23: 0+0j
    dc_offs24: 0+0j
    dc_offs25: 0+0j
    dc_offs26: 0+0j
    dc_offs27: 0+0j
    dc_offs28: 0+0j
    dc_offs29: 0+0j
    dc_offs3: 0+0j
    dc_offs30: 0+0j
    dc_offs31: 0+0j
    dc_offs4: 0+0j
    dc_offs5: 0+0j
    dc_offs6: 0+0j
    dc_offs7: 0+0j
    dc_offs8: 0+0j
    dc_offs9: 0+0j
    dc_offs_enb0: default
    dc_offs_enb1: default
    dc_offs_enb10: default
    dc_offs_enb11: default
    dc_offs_enb12: default
    dc_offs_enb13: default
    dc_offs_enb14: default
    dc_offs_enb15: default
    dc_offs_enb16: default
    dc_offs_enb17: default
    dc_offs_enb18: default
    dc_offs_enb19: default
    dc_offs_enb2: default
    dc_offs_enb20: default
    dc_offs_enb21: default
    dc_offs_enb22: default
    dc_offs_enb23: default
    dc_offs_enb24: default
    dc_offs_enb25: default
    dc_offs_enb26: default
    dc_offs_enb27: default
    dc_offs_enb28: default
    dc_offs_enb29: default
    dc_offs_enb3: default
    dc_offs_enb30: default
    dc_offs_enb31: default
    dc_offs_enb4: default
    dc_offs_enb5: default
    dc_offs_enb6: default
    dc_offs_enb7: default
    dc_offs_enb8: default
    dc_offs_enb9: default
    dev_addr: '""'
    dev_args: IP_INFO
    gain0: '.7'
    gain1: '0'
    gain10: '0'
    gain11: '0'
    gain12: '0'
    gain13: '0'
    gain14: '0'
    gain15: '0'
    gain16: '0'
    gain17: '0'
    gain18: '0'
    gain19: '0'
    gain2: '0'
    gain20: '0'
    gain21: '0'
    gain22: '0'
    gain23: '0'
    gain24: '0'
    gain25: '0'
    gain26: '0'
    gain27: '0'
    gain28: '0'
    gain29: '0'
    gain3: '0'
    gain30: '0'
    gain31: '0'
    gain4: '0'
    gain5: '0'
    gain6: '0'
    gain7: '0'
    gain8: '0'
    gain9: '0'
    gain_type0: normalized
    gain_type1: default
    gain_type10: default
    gain_type11: default
    gain_type12: default
    gain_type13: default
    gain_type14: default
    gain_type15: default
    gain_type16: default
    gain_type17: default
    gain_type18: default
    gain_type19: default
    gain_type2: default
    gain_type20: default
    gain_type21: default
    gain_type22: default
    gain_type23: default
    gain_type24: default
    gain_type25: default
    gain_type26: default
    gain_type27: default
    gain_type28: default
    gain_type29: default
    gain_type3: default
    gain_type30: default
    gain_type31: default
    gain_type4: default
    gain_type5: default
    gain_type6: default
    gain_type7: default
    gain_type8: default
    gain_type9: default
    iq_imbal0: 0+0j
    iq_imbal1: 0+0j
    iq_imbal10: 0+0j
    iq_imbal11: 0+0j
    iq_imbal12: 0+0j
    iq_imbal13: 0+0j
    iq_imbal14: 0+0j
    iq_imbal15: 0+0j
    iq_imbal16: 0+0j
    iq_imbal17: 0+0j
    iq_imbal18: 0+0j
    iq_imbal19: 0+0j
    iq_imbal2: 0+0j
    iq_imbal20: 0+0j
    iq_imbal21: 0+0j
    iq_imbal22: 0+0j
    iq_imbal23: 0+0j
    iq_imbal24: 0+0j
    iq_imbal25: 0+0j
    iq_imbal26: 0+0j
    iq_imbal27: 0+0j
    iq_imbal28: 0+0j
    iq_imbal29: 0+0j
    iq_imbal3: 0+0j
    iq_imbal30: 0+0j
    iq_imbal31: 0+0j
    iq_imbal4: 0+0j
    iq_imbal5: 0+0j
    iq_imbal6: 0+0j
    iq_imbal7: 0+0j
    iq_imbal8: 0+0j
    iq_imbal9: 0+0j
    iq_imbal_enb0: default
    iq_imbal_enb1: default
    iq_imbal_enb10: default
    iq_imbal_enb11: default
    iq_imbal_enb12: default
    iq_imbal_enb13: default
    iq_imbal_enb14: default
    iq_imbal_enb15: default
    iq_imbal_enb16: default
    iq_imbal_enb17: default
    iq_imbal_enb18: default
    iq_imbal_enb19: default
    iq_imbal_enb2: default
    iq_imbal_enb20: default
    iq_imbal_enb21: default
    iq_imbal_enb22: default
    iq_imbal_enb23: default
    iq_imbal_enb24: default
    iq_imbal_enb25: default
    iq_imbal_enb26: default
    iq_imbal_enb27: default
    iq_imbal_enb28: default
    iq_imbal_enb29: default
    iq_imbal_enb3: default
    iq_imbal_enb30: default
    iq_imbal_enb31: default
    iq_imbal_enb4: default
    iq_imbal_enb5: default
    iq_imbal_enb6: default
    iq_imbal_enb7: default
    iq_imbal_enb8: default
    iq_imbal_enb9: default
    lo_export0: 'False'
    lo_export1: 'False'
    lo_export10: 'False'
    lo_export11: 'False'
    lo_export12: 'False'
    lo_export13: 'False'
    lo_export14: 'False'
    lo_export15: 'False'
    lo_export16: 'False'
    lo_export17: 'False'
    lo_export18: 'False'
    lo_export19: 'False'
    lo_export2: 'False'
    lo_export20: 'False'
    lo_export21: 'False'
    lo_export22: 'False'
    lo_export23: 'False'
    lo_export24: 'False'
    lo_export25: 'False'
    lo_export26: 'False'
    lo_export27: 'False'
    lo_export28: 'False'
    lo_export29: 'False'
    lo_export3: 'False'
    lo_export30: 'False'
    lo_export31: 'False'
    lo_export4: 'False'
    lo_export5: 'False'
    lo_export6: 'False'
    lo_export7: 'False'
    lo_export8: 'False'
    lo_export9: 'False'
    lo_source0: internal
    lo_source1: internal
    lo_source10: internal
    lo_source11: internal
    lo_source12: internal
    lo_source13: internal
    lo_source14: internal
    lo_source15: internal
    lo_source16: internal
    lo_source17: internal
    lo_source18: internal
    lo_source19: internal
    lo_source2: internal
    lo_source20: internal
    lo_source21: internal
    lo_source22: internal
    lo_source23: internal
    lo_source24: internal
    lo_source25: internal
    lo_source26: internal
    lo_source27: internal
    lo_source28: internal
    lo_source29: internal
    lo_source3: internal
    lo_source30: internal
    lo_source31: internal
    lo_source4: internal
    lo_source5: internal
    lo_source6: internal
    lo_source7: internal
    lo_source8: internal
    lo_source9: internal
    maxoutbuf: '0'
    minoutbuf: sampleRate
    nchan: '1'
    num_mboards: '1'
    otw: ''
    rx_agc0: Default
    rx_agc1: Default
    rx_agc10: Default
    rx_agc11: Default
    rx_agc12: Default
    rx_agc13: Default
    rx_agc14: Default
    rx_agc15: Default
    rx_agc16: Default
    rx_agc17: Default
    rx_agc18: Default
    rx_agc19: Default
    rx_agc2: Default
    rx_agc20: Default
    rx_agc21: Default
    rx_agc22: Default
    rx_agc23: Default
    rx_agc24: Default
    rx_agc25: Default
    rx_agc26: Default
    rx_agc27: Default
    rx_agc28: Default
    rx_agc29: Default
    rx_agc3: Default
    rx_agc30: Default
    rx_agc31: Default
    rx_agc4: Default
    rx_agc5: Default
    rx_agc6: Default
    rx_agc7: Default
    rx_agc8: Default
    rx_agc9: Default
    samp_rate: sampleRate
    sd_spec0: ''
    sd_spec1: ''
    sd_spec2: ''
    sd_spec3: ''
    sd_spec4: ''
    sd_spec5: ''
    sd_spec6: ''
    sd_spec7: ''
    show_lo_controls: 'False'
    start_time: '-1'
    stream_args: ''
    stream_chans: '[]'
    sync: pc_clock_next_pps
    time_source0: ''
    time_source1: ''
    time_source2: ''
    time_source3: ''
    time_source4: ''
    time_source5: ''
    time_source6: ''
    time_source7: ''
    type: fc32
  states:
    bus_sink: false
    bus_source: false
    bus_structure: null
    coordinate: [72, 172.0]
    rotation: 0
    state: enabled

connections:
- [blocks_stream_to_vector_0, '0', spectrumDetect_specDetect_1, '0']
- [spectrumDetect_specDetect_1, detect_pmt, spectrumDetect_spectrumPlot_0, detect_pmt]
- [uhd_usrp_source_0, '0', blocks_stream_to_vector_0, '0']

metadata:
  file_format: 1
  grc_version: v3.10.9.1-12-gec7b2c40
