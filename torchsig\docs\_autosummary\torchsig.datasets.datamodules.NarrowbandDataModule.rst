torchsig.datasets.datamodules.NarrowbandDataModule
==================================================

.. currentmodule:: torchsig.datasets.datamodules

.. autoclass:: NarrowbandDataModule
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~NarrowbandDataModule.from_datasets
      ~NarrowbandDataModule.load_from_checkpoint
      ~NarrowbandDataModule.load_state_dict
      ~NarrowbandDataModule.on_after_batch_transfer
      ~NarrowbandDataModule.on_before_batch_transfer
      ~NarrowbandDataModule.on_exception
      ~NarrowbandDataModule.predict_dataloader
      ~NarrowbandDataModule.prepare_data
      ~NarrowbandDataModule.save_hyperparameters
      ~NarrowbandDataModule.setup
      ~NarrowbandDataModule.state_dict
      ~NarrowbandDataModule.teardown
      ~NarrowbandDataModule.test_dataloader
      ~NarrowbandDataModule.train_dataloader
      ~NarrowbandDataModule.transfer_batch_to_device
      ~NarrowbandDataModule.val_dataloader
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~NarrowbandDataModule.CHECKPOINT_HYPER_PARAMS_KEY
      ~NarrowbandDataModule.CHECKPOINT_HYPER_PARAMS_NAME
      ~NarrowbandDataModule.CHECKPOINT_HYPER_PARAMS_TYPE
      ~NarrowbandDataModule.hparams
      ~NarrowbandDataModule.hparams_initial
      ~NarrowbandDataModule.name
   
   