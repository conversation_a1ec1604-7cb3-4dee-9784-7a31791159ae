Image Datasets
======================

.. currentmodule:: torchsig.image_datasets

Synthetic spectrogram datasets (not from I/Q data) and tools for signal spectrogram detection and classification. Read more in the `Torchsig GNU Radio Conference 2024 publication <https://events.gnuradio.org/event/24/contributions/628/attachments/190/473/TorchSig_GRCon2024_paper.pdf>`_.

.. contents:: Image Datasets
    :local:

Datasets
----------------

.. automodule:: torchsig.image_datasets.datasets.synthetic_signals
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: torchsig.image_datasets.datasets.file_loading_datasets
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: torchsig.image_datasets.datasets.composites
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: torchsig.image_datasets.datasets.protocols
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: torchsig.image_datasets.datasets.yolo_datasets
    :members:
    :undoc-members:
    :show-inheritance:

Plotting
----------------
.. automodule:: torchsig.image_datasets.plotting.plotting
    :members:
    :undoc-members:
    :show-inheritance:

Transforms
----------------
.. automodule:: torchsig.image_datasets.transforms.denoising
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: torchsig.image_datasets.transforms.impairments
    :members:
    :undoc-members:
    :show-inheritance:

Annotation Tools
----------------
.. automodule:: torchsig.image_datasets.annotation_tools.yolo_annotation_tool
    :members:
    :undoc-members:
    :show-inheritance: