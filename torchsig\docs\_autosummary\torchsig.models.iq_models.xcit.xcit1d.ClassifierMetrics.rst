torchsig.models.iq\_models.xcit.xcit1d.ClassifierMetrics
========================================================

.. currentmodule:: torchsig.models.iq_models.xcit.xcit1d

.. autoclass:: ClassifierMetrics
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~ClassifierMetrics.load_state_dict
      ~ClassifierMetrics.on_after_backward
      ~ClassifierMetrics.on_before_backward
      ~ClassifierMetrics.on_before_optimizer_step
      ~ClassifierMetrics.on_before_zero_grad
      ~ClassifierMetrics.on_exception
      ~ClassifierMetrics.on_fit_end
      ~ClassifierMetrics.on_fit_start
      ~ClassifierMetrics.on_load_checkpoint
      ~ClassifierMetrics.on_predict_batch_end
      ~ClassifierMetrics.on_predict_batch_start
      ~ClassifierMetrics.on_predict_end
      ~ClassifierMetrics.on_predict_epoch_end
      ~ClassifierMetrics.on_predict_epoch_start
      ~ClassifierMetrics.on_predict_start
      ~ClassifierMetrics.on_sanity_check_end
      ~ClassifierMetrics.on_sanity_check_start
      ~ClassifierMetrics.on_save_checkpoint
      ~ClassifierMetrics.on_test_batch_end
      ~ClassifierMetrics.on_test_batch_start
      ~ClassifierMetrics.on_test_end
      ~ClassifierMetrics.on_test_epoch_end
      ~ClassifierMetrics.on_test_epoch_start
      ~ClassifierMetrics.on_test_start
      ~ClassifierMetrics.on_train_batch_end
      ~ClassifierMetrics.on_train_batch_start
      ~ClassifierMetrics.on_train_end
      ~ClassifierMetrics.on_train_epoch_end
      ~ClassifierMetrics.on_train_epoch_start
      ~ClassifierMetrics.on_train_start
      ~ClassifierMetrics.on_validation_batch_end
      ~ClassifierMetrics.on_validation_batch_start
      ~ClassifierMetrics.on_validation_end
      ~ClassifierMetrics.on_validation_epoch_end
      ~ClassifierMetrics.on_validation_epoch_start
      ~ClassifierMetrics.on_validation_start
      ~ClassifierMetrics.setup
      ~ClassifierMetrics.state_dict
      ~ClassifierMetrics.teardown
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ClassifierMetrics.state_key
   
   