torchsig.models.model\_utils.model\_utils\_1d.layers\_1d.SqueezeExcite1d
========================================================================

.. currentmodule:: torchsig.models.model_utils.model_utils_1d.layers_1d

.. autoclass:: SqueezeExcite1d
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~SqueezeExcite1d.add_module
      ~SqueezeExcite1d.apply
      ~SqueezeExcite1d.bfloat16
      ~SqueezeExcite1d.buffers
      ~SqueezeExcite1d.children
      ~SqueezeExcite1d.compile
      ~SqueezeExcite1d.cpu
      ~SqueezeExcite1d.cuda
      ~SqueezeExcite1d.double
      ~SqueezeExcite1d.eval
      ~SqueezeExcite1d.extra_repr
      ~SqueezeExcite1d.float
      ~SqueezeExcite1d.forward
      ~SqueezeExcite1d.get_buffer
      ~SqueezeExcite1d.get_extra_state
      ~SqueezeExcite1d.get_parameter
      ~SqueezeExcite1d.get_submodule
      ~SqueezeExcite1d.half
      ~SqueezeExcite1d.ipu
      ~SqueezeExcite1d.load_state_dict
      ~SqueezeExcite1d.modules
      ~SqueezeExcite1d.mtia
      ~SqueezeExcite1d.named_buffers
      ~SqueezeExcite1d.named_children
      ~SqueezeExcite1d.named_modules
      ~SqueezeExcite1d.named_parameters
      ~SqueezeExcite1d.parameters
      ~SqueezeExcite1d.register_backward_hook
      ~SqueezeExcite1d.register_buffer
      ~SqueezeExcite1d.register_forward_hook
      ~SqueezeExcite1d.register_forward_pre_hook
      ~SqueezeExcite1d.register_full_backward_hook
      ~SqueezeExcite1d.register_full_backward_pre_hook
      ~SqueezeExcite1d.register_load_state_dict_post_hook
      ~SqueezeExcite1d.register_load_state_dict_pre_hook
      ~SqueezeExcite1d.register_module
      ~SqueezeExcite1d.register_parameter
      ~SqueezeExcite1d.register_state_dict_post_hook
      ~SqueezeExcite1d.register_state_dict_pre_hook
      ~SqueezeExcite1d.requires_grad_
      ~SqueezeExcite1d.set_extra_state
      ~SqueezeExcite1d.set_submodule
      ~SqueezeExcite1d.share_memory
      ~SqueezeExcite1d.state_dict
      ~SqueezeExcite1d.to
      ~SqueezeExcite1d.to_empty
      ~SqueezeExcite1d.train
      ~SqueezeExcite1d.type
      ~SqueezeExcite1d.xpu
      ~SqueezeExcite1d.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~SqueezeExcite1d.T_destination
      ~SqueezeExcite1d.call_super_init
      ~SqueezeExcite1d.dump_patches
      ~SqueezeExcite1d.training
   
   