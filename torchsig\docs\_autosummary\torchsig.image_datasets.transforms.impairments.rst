torchsig.image\_datasets.transforms.impairments
===============================================

.. automodule:: torchsig.image_datasets.transforms.impairments

   
   
   

   
   
   .. rubric:: Functions

   .. autosummary::
      :toctree:
      :nosignatures:
   
      pad_border
      scale_dynamic_range
   
   

   
   
   .. rubric:: Classes

   .. autosummary::
      :toctree:
      :template: custom_class_template.rst
      :nosignatures:
   
      BlurTransform
      GaussianNoiseTransform
      RandomGaussianNoiseTransform
      RandomImageResizeTransform
      RandomRippleNoiseTransform
      RippleNoiseTransform
      ScaleTransform
   
   

   
   
   



