torchsig.models.spectrogram\_models.detr.modules.Chunker
========================================================

.. currentmodule:: torchsig.models.spectrogram_models.detr.modules

.. autoclass:: Chunker
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Chunker.add_module
      ~Chunker.apply
      ~Chunker.bfloat16
      ~Chunker.buffers
      ~Chunker.children
      ~Chunker.compile
      ~Chunker.cpu
      ~Chunker.cuda
      ~Chunker.double
      ~Chunker.eval
      ~Chunker.extra_repr
      ~Chunker.float
      ~Chunker.forward
      ~Chunker.get_buffer
      ~Chunker.get_extra_state
      ~Chunker.get_parameter
      ~Chunker.get_submodule
      ~Chunker.half
      ~Chunker.ipu
      ~Chunker.load_state_dict
      ~Chunker.modules
      ~Chunker.mtia
      ~Chunker.named_buffers
      ~Chunker.named_children
      ~Chunker.named_modules
      ~Chunker.named_parameters
      ~Chunker.parameters
      ~Chunker.register_backward_hook
      ~Chunker.register_buffer
      ~Chunker.register_forward_hook
      ~Chunker.register_forward_pre_hook
      ~Chunker.register_full_backward_hook
      ~Chunker.register_full_backward_pre_hook
      ~Chunker.register_load_state_dict_post_hook
      ~Chunker.register_load_state_dict_pre_hook
      ~Chunker.register_module
      ~Chunker.register_parameter
      ~Chunker.register_state_dict_post_hook
      ~Chunker.register_state_dict_pre_hook
      ~Chunker.requires_grad_
      ~Chunker.set_extra_state
      ~Chunker.set_submodule
      ~Chunker.share_memory
      ~Chunker.state_dict
      ~Chunker.to
      ~Chunker.to_empty
      ~Chunker.train
      ~Chunker.type
      ~Chunker.xpu
      ~Chunker.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~Chunker.T_destination
      ~Chunker.call_super_init
      ~Chunker.dump_patches
      ~Chunker.training
   
   