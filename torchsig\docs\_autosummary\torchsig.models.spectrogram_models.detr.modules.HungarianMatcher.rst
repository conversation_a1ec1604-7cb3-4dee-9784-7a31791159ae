torchsig.models.spectrogram\_models.detr.modules.HungarianMatcher
=================================================================

.. currentmodule:: torchsig.models.spectrogram_models.detr.modules

.. autoclass:: HungarianMatcher
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~HungarianMatcher.add_module
      ~HungarianMatcher.apply
      ~HungarianMatcher.bfloat16
      ~HungarianMatcher.buffers
      ~HungarianMatcher.children
      ~HungarianMatcher.compile
      ~HungarianMatcher.cpu
      ~HungarianMatcher.cuda
      ~HungarianMatcher.double
      ~HungarianMatcher.eval
      ~HungarianMatcher.extra_repr
      ~HungarianMatcher.float
      ~HungarianMatcher.forward
      ~HungarianMatcher.get_buffer
      ~HungarianMatcher.get_extra_state
      ~HungarianMatcher.get_parameter
      ~HungarianMatcher.get_submodule
      ~HungarianMatcher.half
      ~HungarianMatcher.ipu
      ~HungarianMatcher.load_state_dict
      ~HungarianMatcher.modules
      ~HungarianMatcher.mtia
      ~HungarianMatcher.named_buffers
      ~HungarianMatcher.named_children
      ~HungarianMatcher.named_modules
      ~HungarianMatcher.named_parameters
      ~HungarianMatcher.parameters
      ~HungarianMatcher.register_backward_hook
      ~HungarianMatcher.register_buffer
      ~HungarianMatcher.register_forward_hook
      ~HungarianMatcher.register_forward_pre_hook
      ~HungarianMatcher.register_full_backward_hook
      ~HungarianMatcher.register_full_backward_pre_hook
      ~HungarianMatcher.register_load_state_dict_post_hook
      ~HungarianMatcher.register_load_state_dict_pre_hook
      ~HungarianMatcher.register_module
      ~HungarianMatcher.register_parameter
      ~HungarianMatcher.register_state_dict_post_hook
      ~HungarianMatcher.register_state_dict_pre_hook
      ~HungarianMatcher.requires_grad_
      ~HungarianMatcher.set_extra_state
      ~HungarianMatcher.set_submodule
      ~HungarianMatcher.share_memory
      ~HungarianMatcher.state_dict
      ~HungarianMatcher.to
      ~HungarianMatcher.to_empty
      ~HungarianMatcher.train
      ~HungarianMatcher.type
      ~HungarianMatcher.xpu
      ~HungarianMatcher.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~HungarianMatcher.T_destination
      ~HungarianMatcher.call_super_init
      ~HungarianMatcher.dump_patches
      ~HungarianMatcher.training
   
   