title: The SPECTRUMDETECT OOT Module
brief: Short description of gr-spectrumDetect
tags: # Tags are arbitrary, but look at CGRAN what other authors are using
  - sdr
author:
  - Author Name <<EMAIL>>
copyright_owner:
  - Copyright Owner 1
license:
gr_supported_version: # Put a comma separated list of supported GR versions here
#repo: # Put the URL of the repository here, or leave blank for default
#website: <module_website> # If you have a separate project website, put it here
#icon: <icon_url> # Put a URL to a square image here that will be used as an icon on CGRAN
---
A longer, multi-line description of gr-spectrumDetect.
You may use some *basic* Markdown here.
If left empty, it will try to find a README file instead.
