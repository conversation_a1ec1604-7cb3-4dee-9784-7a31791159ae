torchsig.models.iq\_models.xcit.xcit1d.ConvDownSampler
======================================================

.. currentmodule:: torchsig.models.iq_models.xcit.xcit1d

.. autoclass:: ConvDownSampler
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~ConvDownSampler.add_module
      ~ConvDownSampler.apply
      ~ConvDownSampler.bfloat16
      ~ConvDownSampler.buffers
      ~ConvDownSampler.children
      ~ConvDownSampler.compile
      ~ConvDownSampler.cpu
      ~ConvDownSampler.cuda
      ~ConvDownSampler.double
      ~ConvDownSampler.eval
      ~ConvDownSampler.extra_repr
      ~ConvDownSampler.float
      ~ConvDownSampler.forward
      ~ConvDownSampler.get_buffer
      ~ConvDownSampler.get_extra_state
      ~ConvDownSampler.get_parameter
      ~ConvDownSampler.get_submodule
      ~ConvDownSampler.half
      ~ConvDownSampler.ipu
      ~ConvDownSampler.load_state_dict
      ~ConvDownSampler.modules
      ~ConvDownSampler.mtia
      ~ConvDownSampler.named_buffers
      ~ConvDownSampler.named_children
      ~ConvDownSampler.named_modules
      ~ConvDownSampler.named_parameters
      ~ConvDownSampler.parameters
      ~ConvDownSampler.register_backward_hook
      ~ConvDownSampler.register_buffer
      ~ConvDownSampler.register_forward_hook
      ~ConvDownSampler.register_forward_pre_hook
      ~ConvDownSampler.register_full_backward_hook
      ~ConvDownSampler.register_full_backward_pre_hook
      ~ConvDownSampler.register_load_state_dict_post_hook
      ~ConvDownSampler.register_load_state_dict_pre_hook
      ~ConvDownSampler.register_module
      ~ConvDownSampler.register_parameter
      ~ConvDownSampler.register_state_dict_post_hook
      ~ConvDownSampler.register_state_dict_pre_hook
      ~ConvDownSampler.requires_grad_
      ~ConvDownSampler.set_extra_state
      ~ConvDownSampler.set_submodule
      ~ConvDownSampler.share_memory
      ~ConvDownSampler.state_dict
      ~ConvDownSampler.to
      ~ConvDownSampler.to_empty
      ~ConvDownSampler.train
      ~ConvDownSampler.type
      ~ConvDownSampler.xpu
      ~ConvDownSampler.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ConvDownSampler.T_destination
      ~ConvDownSampler.call_super_init
      ~ConvDownSampler.dump_patches
      ~ConvDownSampler.training
   
   