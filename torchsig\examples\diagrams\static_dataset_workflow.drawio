<mxfile host="65bd71144e">
    <diagram id="l_8ZM9o4uYeIKSSzSNa-" name="Page-1">
        <mxGraphModel dx="1249" dy="959" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" background="#ffffff" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="3" value="StaticDataset __getitem__ Workflow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=36;" vertex="1" parent="1">
                    <mxGeometry x="20" y="10" width="820" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=9;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="5" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;Target Transforms: produce labels requested from user&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; background-color: initial; font-family: &amp;quot;Source Code Pro&amp;quot;;&quot;&gt;targets = self.dataset_metadata.target_transforms(sample)&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;" vertex="1" parent="1">
                    <mxGeometry x="59.99000000000001" y="850" width="351.25" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Source+Code+Pro&quot; face=&quot;Source Code Pro&quot;&gt;return data, targets&lt;/font&gt;&lt;/span&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;" vertex="1" parent="1">
                    <mxGeometry x="318" y="990" width="220" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=9;exitX=0.5;exitY=1.017;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="14" target="5">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="271.22" y="870" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="apply" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="11">
                    <mxGeometry x="-0.4173" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="DatasetDict" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="145.60000000000002" y="730" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="data: np.ndarray&lt;br&gt;metadata: [dict]" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=12;" vertex="1" parent="13">
                    <mxGeometry y="30" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;fontSize=9;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="38" target="24">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="239.18" y="400" as="sourcePoint"/>
                        <mxPoint x="224.18" y="350" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="create" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="15">
                    <mxGeometry x="-0.2149" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" style="edgeStyle=none;html=1;fontSize=9;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="21" target="13">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="481.23" y="860" as="sourcePoint"/>
                        <mxPoint x="194.18" y="750" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="convert to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="19">
                    <mxGeometry x="-0.2842" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;User Transforms: defined by user, applied to whole IQ cut, includes ML transforms&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; font-family: &amp;quot;Source Code Pro&amp;quot;; background-color: initial;&quot;&gt;sample = self.dataset_metadata.transforms(sample)&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;" vertex="1" parent="1">
                    <mxGeometry x="81.85000000000001" y="610" width="307.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="22" style="edgeStyle=none;html=1;fontSize=9;exitX=0.498;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="25" target="34">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="384.79" y="535" as="targetPoint"/>
                        <mxPoint x="325.29" y="519.9000000000001" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="apply" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="22">
                    <mxGeometry x="-0.3046" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="DatasetSignal" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="145.61" y="340" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="data: np.ndarray&lt;br&gt;metadata: [SignalMetadata]" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=12;" vertex="1" parent="24">
                    <mxGeometry y="30" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;Dataset Transforms: applied to whole IQ cut&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 10px; font-family: &amp;quot;Source Code Pro&amp;quot;; background-color: initial;&quot;&gt;sample = self.impairment_transforms(sample)&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=36;align=center;verticalAlign=middle;" vertex="1" parent="1">
                    <mxGeometry x="90.6" y="480" width="290" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="35" style="edgeStyle=none;html=1;fontSize=9;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="34" target="21">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="414.8" y="560" as="targetPoint"/>
                        <mxPoint x="339.61999999999995" y="559.45" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="apply" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="35">
                    <mxGeometry x="-0.3046" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="37" target="38">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="raw" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="41">
                    <mxGeometry x="0.1298" y="-1" relative="1" as="geometry">
                        <mxPoint x="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="37" target="39">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="processed" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="43">
                    <mxGeometry x="-0.1087" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;is dataset on disk raw or processed?&lt;/font&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;align=center;verticalAlign=middle;" vertex="1" parent="1">
                    <mxGeometry x="313" y="90" width="225" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;load IQ data and signal metadata from disk&lt;br&gt;&lt;br&gt;data, signal_metadatas = load(index)&lt;br&gt;&lt;/font&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;align=center;verticalAlign=middle;" vertex="1" parent="1">
                    <mxGeometry x="104.05000000000001" y="210" width="263.13" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="40" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="39" target="10">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="510" y="690" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="39" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;load data and targets from disk&lt;br&gt;&lt;br&gt;data, targets = load(index)&lt;br&gt;&lt;/font&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;line-height: 100%; font-size: 14px;&quot;&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;align=center;verticalAlign=middle;" vertex="1" parent="1">
                    <mxGeometry x="510" y="210" width="263.13" height="90" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>