torchsig.transforms.dataset\_transforms.SpectralInversionDatasetTransform
=========================================================================

.. currentmodule:: torchsig.transforms.dataset_transforms

.. autoclass:: SpectralInversionDatasetTransform
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~SpectralInversionDatasetTransform.add_parent
      ~SpectralInversionDatasetTransform.get_second_seed
      ~SpectralInversionDatasetTransform.seed
      ~SpectralInversionDatasetTransform.setup_rngs
      ~SpectralInversionDatasetTransform.update
      ~SpectralInversionDatasetTransform.update_from_parent
   
   

   
   
   