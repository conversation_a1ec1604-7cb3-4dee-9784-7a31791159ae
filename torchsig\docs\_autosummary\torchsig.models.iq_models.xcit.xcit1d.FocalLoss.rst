torchsig.models.iq\_models.xcit.xcit1d.FocalLoss
================================================

.. currentmodule:: torchsig.models.iq_models.xcit.xcit1d

.. autoclass:: FocalLoss
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~FocalLoss.add_module
      ~FocalLoss.apply
      ~FocalLoss.bfloat16
      ~FocalLoss.buffers
      ~FocalLoss.children
      ~FocalLoss.compile
      ~FocalLoss.cpu
      ~FocalLoss.cuda
      ~FocalLoss.double
      ~FocalLoss.eval
      ~FocalLoss.extra_repr
      ~FocalLoss.float
      ~FocalLoss.forward
      ~FocalLoss.get_buffer
      ~FocalLoss.get_extra_state
      ~FocalLoss.get_parameter
      ~FocalLoss.get_submodule
      ~FocalLoss.half
      ~FocalLoss.ipu
      ~FocalLoss.load_state_dict
      ~FocalLoss.modules
      ~FocalLoss.mtia
      ~FocalLoss.named_buffers
      ~FocalLoss.named_children
      ~FocalLoss.named_modules
      ~FocalLoss.named_parameters
      ~FocalLoss.parameters
      ~FocalLoss.register_backward_hook
      ~FocalLoss.register_buffer
      ~FocalLoss.register_forward_hook
      ~FocalLoss.register_forward_pre_hook
      ~FocalLoss.register_full_backward_hook
      ~FocalLoss.register_full_backward_pre_hook
      ~FocalLoss.register_load_state_dict_post_hook
      ~FocalLoss.register_load_state_dict_pre_hook
      ~FocalLoss.register_module
      ~FocalLoss.register_parameter
      ~FocalLoss.register_state_dict_post_hook
      ~FocalLoss.register_state_dict_pre_hook
      ~FocalLoss.requires_grad_
      ~FocalLoss.set_extra_state
      ~FocalLoss.set_submodule
      ~FocalLoss.share_memory
      ~FocalLoss.state_dict
      ~FocalLoss.to
      ~FocalLoss.to_empty
      ~FocalLoss.train
      ~FocalLoss.type
      ~FocalLoss.xpu
      ~FocalLoss.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~FocalLoss.T_destination
      ~FocalLoss.call_super_init
      ~FocalLoss.dump_patches
      ~FocalLoss.training
   
   