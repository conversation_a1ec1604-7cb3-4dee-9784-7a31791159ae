torchsig.models.model\_utils.general\_layers.ScalingLayer
=========================================================

.. currentmodule:: torchsig.models.model_utils.general_layers

.. autoclass:: ScalingLayer
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~ScalingLayer.add_module
      ~ScalingLayer.apply
      ~ScalingLayer.bfloat16
      ~ScalingLayer.buffers
      ~ScalingLayer.children
      ~ScalingLayer.compile
      ~ScalingLayer.cpu
      ~ScalingLayer.cuda
      ~ScalingLayer.double
      ~ScalingLayer.eval
      ~ScalingLayer.extra_repr
      ~ScalingLayer.float
      ~ScalingLayer.forward
      ~ScalingLayer.get_buffer
      ~ScalingLayer.get_extra_state
      ~<PERSON>aling<PERSON>ayer.get_parameter
      ~ScalingLayer.get_submodule
      ~<PERSON>aling<PERSON>ayer.half
      ~ScalingLayer.ipu
      ~ScalingLayer.load_state_dict
      ~ScalingLayer.modules
      ~ScalingLayer.mtia
      ~ScalingLayer.named_buffers
      ~ScalingLayer.named_children
      ~ScalingLayer.named_modules
      ~ScalingLayer.named_parameters
      ~ScalingLayer.parameters
      ~ScalingLayer.register_backward_hook
      ~ScalingLayer.register_buffer
      ~ScalingLayer.register_forward_hook
      ~ScalingLayer.register_forward_pre_hook
      ~ScalingLayer.register_full_backward_hook
      ~ScalingLayer.register_full_backward_pre_hook
      ~ScalingLayer.register_load_state_dict_post_hook
      ~ScalingLayer.register_load_state_dict_pre_hook
      ~ScalingLayer.register_module
      ~ScalingLayer.register_parameter
      ~ScalingLayer.register_state_dict_post_hook
      ~ScalingLayer.register_state_dict_pre_hook
      ~ScalingLayer.requires_grad_
      ~ScalingLayer.set_extra_state
      ~ScalingLayer.set_submodule
      ~ScalingLayer.share_memory
      ~ScalingLayer.state_dict
      ~ScalingLayer.to
      ~ScalingLayer.to_empty
      ~ScalingLayer.train
      ~ScalingLayer.type
      ~ScalingLayer.xpu
      ~ScalingLayer.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ScalingLayer.T_destination
      ~ScalingLayer.call_super_init
      ~ScalingLayer.dump_patches
      ~ScalingLayer.training
   
   