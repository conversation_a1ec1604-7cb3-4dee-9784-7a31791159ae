torchsig.transforms.dataset\_transforms.Spectrogram
===================================================

.. currentmodule:: torchsig.transforms.dataset_transforms

.. autoclass:: Spectrogram
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Spectrogram.add_parent
      ~Spectrogram.get_second_seed
      ~Spectrogram.seed
      ~Spectrogram.setup_rngs
      ~Spectrogram.update
      ~Spectrogram.update_from_parent
   
   

   
   
   