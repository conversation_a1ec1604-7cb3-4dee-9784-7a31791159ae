torchsig.transforms.target\_transforms.Bandwidth
================================================

.. currentmodule:: torchsig.transforms.target_transforms

.. autoclass:: Bandwidth
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Bandwidth.add_parent
      ~Bandwidth.get_second_seed
      ~Bandwidth.seed
      ~Bandwidth.setup_rngs
      ~Bandwidth.update
      ~Bandwidth.update_from_parent
   
   

   
   
   