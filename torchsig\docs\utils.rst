Utilities
======================

Extra utilities such as writing and reading to disk or type checking are included in the `torchsig/utils` folder.

The following utilities are available:

.. contents:: Utilities
    :local:


Digital Signal Processing Utils
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.. automodule:: torchsig.utils.dsp
    :members:
    :undoc-members:
    :show-inheritance:


Dataset Utils
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: torchsig.utils.generate
    :members:
    :undoc-members:
    :show-inheritance:


Reading/Writing Utils
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Writer
-------------------------------------
.. automodule:: torchsig.utils.writer
    :members:
    :undoc-members:
    :show-inheritance:

YAML Utils
-------------------------------------
.. automodule:: torchsig.utils.yaml
    :members:
    :undoc-members:
    :show-inheritance:

File Handlers
-------------------------------------
.. automodule:: torchsig.utils.file_handlers.base_handler
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: torchsig.utils.file_handlers.zarr
    :members:
    :undoc-members:
    :show-inheritance:

Variable and Data Verification Utils
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: torchsig.utils.verify
    :members:
    :undoc-members:
    :show-inheritance:

Printing Utils
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: torchsig.utils.printing
    :members:
    :undoc-members:
    :show-inheritance:


Randomization Utils
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: torchsig.utils.random
    :members:
    :undoc-members:
    :show-inheritance: