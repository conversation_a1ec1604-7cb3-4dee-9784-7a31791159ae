torchsig.transforms.target\_transforms.ClassName
================================================

.. currentmodule:: torchsig.transforms.target_transforms

.. autoclass:: ClassName
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~ClassName.add_parent
      ~ClassName.get_second_seed
      ~ClassName.seed
      ~ClassName.setup_rngs
      ~ClassName.update
      ~ClassName.update_from_parent
   
   

   
   
   