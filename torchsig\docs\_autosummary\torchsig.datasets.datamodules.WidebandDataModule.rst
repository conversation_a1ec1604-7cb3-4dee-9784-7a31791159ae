torchsig.datasets.datamodules.WidebandDataModule
================================================

.. currentmodule:: torchsig.datasets.datamodules

.. autoclass:: WidebandDataModule
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~WidebandDataModule.from_datasets
      ~WidebandDataModule.load_from_checkpoint
      ~WidebandDataModule.load_state_dict
      ~WidebandDataModule.on_after_batch_transfer
      ~WidebandDataModule.on_before_batch_transfer
      ~WidebandDataModule.on_exception
      ~WidebandDataModule.predict_dataloader
      ~WidebandDataModule.prepare_data
      ~WidebandDataModule.save_hyperparameters
      ~WidebandDataModule.setup
      ~WidebandDataModule.state_dict
      ~WidebandDataModule.teardown
      ~WidebandDataModule.test_dataloader
      ~WidebandDataModule.train_dataloader
      ~WidebandDataModule.transfer_batch_to_device
      ~WidebandDataModule.val_dataloader
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~WidebandDataModule.CHECKPOINT_HYPER_PARAMS_KEY
      ~WidebandDataModule.CHECKPOINT_HYPER_PARAMS_NAME
      ~WidebandDataModule.CHECKPOINT_HYPER_PARAMS_TYPE
      ~WidebandDataModule.hparams
      ~WidebandDataModule.hparams_initial
      ~WidebandDataModule.name
   
   