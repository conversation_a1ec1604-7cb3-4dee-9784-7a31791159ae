torchsig.models.model\_utils.model\_utils\_1d.conversions\_to\_1d
=================================================================

.. automodule:: torchsig.models.model_utils.model_utils_1d.conversions_to_1d

   
   
   

   
   
   .. rubric:: Functions

   .. autosummary::
      :toctree:
      :nosignatures:
   
      avgpool2d_to_avgpool1d
      batchNorm2d_to_GBN1d
      batchNorm2d_to_batchNorm1d
      conv2d_to_conv1d
      convert_2d_model_to_1d
      get_1d_kernel
      make_fast_avg_pooling_layer
      maxpool2d_to_maxpool1d
      squeezeExcite_to_squeezeExcite1d
      try_default
   
   

   
   
   

   
   
   



