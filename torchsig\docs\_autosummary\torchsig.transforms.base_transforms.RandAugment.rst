torchsig.transforms.base\_transforms.RandAugment
================================================

.. currentmodule:: torchsig.transforms.base_transforms

.. autoclass:: RandAugment
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~RandAugment.add_parent
      ~RandAugment.get_second_seed
      ~RandAugment.seed
      ~RandAugment.setup_rngs
      ~RandAugment.update
      ~RandAugment.update_from_parent
   
   

   
   
   