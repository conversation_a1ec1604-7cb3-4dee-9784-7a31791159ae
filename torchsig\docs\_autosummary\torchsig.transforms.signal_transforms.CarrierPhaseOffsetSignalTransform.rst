torchsig.transforms.signal\_transforms.CarrierPhaseOffsetSignalTransform
========================================================================

.. currentmodule:: torchsig.transforms.signal_transforms

.. autoclass:: CarrierPhaseOffsetSignalTransform
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~CarrierPhaseOffsetSignalTransform.add_parent
      ~CarrierPhaseOffsetSignalTransform.get_second_seed
      ~CarrierPhaseOffsetSignalTransform.seed
      ~CarrierPhaseOffsetSignalTransform.setup_rngs
      ~CarrierPhaseOffsetSignalTransform.update
      ~CarrierPhaseOffsetSignalTransform.update_from_parent
   
   

   
   
   