<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="831px" height="899px" viewBox="-0.5 -0.5 831 899" content="&lt;mxfile&gt;&lt;diagram id=&quot;laA0arsnX7Pyb7320zwb&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(0, 0, 0);"><defs><style type="text/css">/* cyrillic-ext */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url("data:application/font-woff2;charset=utf-8;base64,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") format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url("data:application/font-woff2;charset=utf-8;base64,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") format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url("data:application/font-woff2;charset=utf-8;base64,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") format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url("data:application/font-woff2;charset=utf-8;base64,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") format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url("data:application/font-woff2;charset=utf-8;base64,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") format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url("data:application/font-woff2;charset=utf-8;base64,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") format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url("data:application/font-woff2;charset=utf-8;base64,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") format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

</style></defs><g><rect x="30" y="100" width="770" height="350" fill="#000000" stroke="#ffffff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 768px; height: 1px; padding-top: 97px; margin-left: 31px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%; font-size: 12px;"><font face="Source Code Pro" data-font-src="https://fonts.googleapis.com/css?family=Source+Code+Pro">self.__generate_new_signal__()</font></p></div></div></div></foreignObject><text x="415" y="97" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="36px" text-anchor="middle">self.__generate_new_signal__()</text></switch></g><rect x="10" y="0" width="820" height="60" fill="#000000" stroke="#ffffff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 818px; height: 1px; padding-top: 30px; margin-left: 11px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#ffffff">NewDataset __getitem__ Workflow</font></div></div></div></foreignObject><text x="420" y="41" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="36px" text-anchor="middle">NewDataset __getitem__ Workflow</text></switch></g><path d="M 351.25 840 L 394.25 840" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 399.5 840 L 392.5 843.5 L 394.25 840 L 392.5 836.5 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="800" width="351.25" height="80" fill="#000000" stroke="#ffffff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 349px; height: 1px; padding-top: 840px; margin-left: 1px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%; font-size: 12px;">Target Transforms: produce labels requested from user</p><p style="line-height: 100%; font-size: 12px;"><span style="font-size: 10px; background-color: initial; font-family: &quot;Source Code Pro&quot;;">targets = self.dataset_metadata.target_transforms(sample)</span></p><p style="line-height: 100%;"></p><p style="line-height: 100%;"></p></div></div></div></foreignObject><text x="176" y="851" fill="#FFFFFF" font-family="Helvetica" font-size="36px" text-anchor="middle">Target Transforms: p...</text></switch></g><path d="M 722.63 230.9 L 722.51 293.63" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 722.5 298.88 L 719.02 291.88 L 722.51 293.63 L 726.02 291.89 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 259px; margin-left: 721px;"><div data-drawio-colors="color: #FFFFFF; background-color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; background-color: rgb(0, 0, 0); white-space: nowrap;">apply</div></div></div></foreignObject><text x="721" y="262" fill="#FFFFFF" font-family="Helvetica" font-size="9px" text-anchor="middle">apply</text></switch></g><path d="M 650 180 L 650 150 L 785 150 L 785 180" fill="#000000" stroke="#ffffff" stroke-miterlimit="10" pointer-events="all"/><path d="M 650 180 L 650 230 L 785 230 L 785 180" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 650 180 L 785 180" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 165px; margin-left: 651px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#ffffff">Signal</font></div></div></div></foreignObject><text x="718" y="169" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Signal</text></switch></g><rect x="650" y="180" width="135" height="50" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 125px; height: 1px; padding-top: 205px; margin-left: 656px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 46px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#ffffff">data: np.ndarray<br />metadata: SignalMetadata</font></div></div></div></foreignObject><text x="656" y="209" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">data: np.ndarray...</text></switch></g><rect x="400.62" y="800" width="220" height="80" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 840px; margin-left: 402px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%;"><span style="font-size: 12px;"><font face="Source Code Pro" data-font-src="https://fonts.googleapis.com/css?family=Source+Code+Pro">return sample.data, targets</font></span></p></div></div></div></foreignObject><text x="511" y="851" fill="#FFFFFF" font-family="Helvetica" font-size="36px" text-anchor="middle">return sampl...</text></switch></g><path d="M 175.62 730.85 L 175.62 793.63" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 175.62 798.88 L 172.12 791.88 L 175.62 793.63 L 179.12 791.88 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 751px; margin-left: 176px;"><div data-drawio-colors="color: #FFFFFF; background-color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; background-color: rgb(0, 0, 0); white-space: nowrap;">apply</div></div></div></foreignObject><text x="176" y="754" fill="#FFFFFF" font-family="Helvetica" font-size="9px" text-anchor="middle">apply</text></switch></g><path d="M 85.62 680 L 85.62 650 L 265.62 650 L 265.62 680" fill="#000000" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 85.62 680 L 85.62 730 L 265.62 730 L 265.62 680" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 85.62 680 L 265.62 680" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 665px; margin-left: 87px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">DatasetDict</div></div></div></foreignObject><text x="176" y="669" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">DatasetDict</text></switch></g><rect x="85.62" y="680" width="180" height="50" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 170px; height: 1px; padding-top: 705px; margin-left: 92px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 46px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">data: np.ndarray<br />metadata: [dict]</div></div></div></foreignObject><text x="92" y="709" fill="#FFFFFF" font-family="Helvetica" font-size="12px">data: np.ndarray...</text></switch></g><path d="M 175 390 L 175.58 493.63" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 175.61 498.88 L 172.07 491.9 L 175.58 493.63 L 179.07 491.86 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 433px; margin-left: 178px;"><div data-drawio-colors="color: #FFFFFF; background-color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; background-color: rgb(0, 0, 0); white-space: nowrap;">create</div></div></div></foreignObject><text x="178" y="436" fill="#FFFFFF" font-family="Helvetica" font-size="9px" text-anchor="middle">create</text></switch></g><path d="M 535 345 L 306.37 345" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 301.12 345 L 308.12 341.5 L 306.37 345 L 308.12 348.5 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><rect x="535" y="300" width="250" height="90" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 345px; margin-left: 536px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%; font-size: 12px;">Signal Transforms: applied to isolated signals</p><p style="line-height: 100%; font-size: 12px;"></p><p style="line-height: 100%; font-size: 12px;"><span style="background-color: initial; font-family: &quot;Source Code Pro&quot;; font-size: 10px;">new_signal = self.impairments(new_signal)</span></p><p style="line-height: 100%;"></p><p style="line-height: 100%;"></p></div></div></div></foreignObject><text x="660" y="356" fill="#FFFFFF" font-family="Helvetica" font-size="36px" text-anchor="middle">Signal Transfo...</text></switch></g><path d="M 331.87 690 L 272.71 689.73" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 267.46 689.71 L 274.47 686.24 L 272.71 689.73 L 274.44 693.24 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 689px; margin-left: 309px;"><div data-drawio-colors="color: #FFFFFF; background-color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; background-color: rgb(0, 0, 0); white-space: nowrap;">convert to</div></div></div></foreignObject><text x="309" y="692" fill="#FFFFFF" font-family="Helvetica" font-size="9px" text-anchor="middle">convert to</text></switch></g><rect x="331.87" y="650" width="307.5" height="80" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 306px; height: 1px; padding-top: 690px; margin-left: 333px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%; font-size: 12px;">User Transforms: defined by user, applied to whole IQ cut, includes ML transforms</p><p style="line-height: 100%; font-size: 12px;"><span style="font-size: 10px; font-family: &quot;Source Code Pro&quot;; background-color: initial;">sample = self.dataset_metadata.transforms(sample)</span></p><p style="line-height: 100%;"></p></div></div></div></foreignObject><text x="486" y="701" fill="#FFFFFF" font-family="Helvetica" font-size="36px" text-anchor="middle">User Transforms:...</text></switch></g><path d="M 265.44 539.45 L 334.25 539.95" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 339.5 539.99 L 332.48 543.44 L 334.25 539.95 L 332.53 536.44 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 540px; margin-left: 292px;"><div data-drawio-colors="color: #FFFFFF; background-color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; background-color: rgb(0, 0, 0); white-space: nowrap;">apply</div></div></div></foreignObject><text x="292" y="543" fill="#FFFFFF" font-family="Helvetica" font-size="9px" text-anchor="middle">apply</text></switch></g><path d="M 85.62 530 L 85.62 500 L 265.62 500 L 265.62 530" fill="#000000" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 85.62 530 L 85.62 580 L 265.62 580 L 265.62 530" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 85.62 530 L 265.62 530" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 515px; margin-left: 87px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">DatasetSignal</div></div></div></foreignObject><text x="176" y="519" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">DatasetSignal</text></switch></g><rect x="85.62" y="530" width="180" height="50" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 170px; height: 1px; padding-top: 555px; margin-left: 92px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 46px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">data: np.ndarray<br />metadata: [SignalMetadata]</div></div></div></foreignObject><text x="92" y="559" fill="#FFFFFF" font-family="Helvetica" font-size="12px">data: np.ndarray...</text></switch></g><path d="M 590 179.16 L 643.77 178.75" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 649.02 178.71 L 642.04 182.26 L 643.77 178.75 L 641.99 175.26 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 177px; margin-left: 613px;"><div data-drawio-colors="color: #FFFFFF; background-color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; background-color: rgb(0, 0, 0); white-space: nowrap;">returns</div></div></div></foreignObject><text x="613" y="180" fill="#FFFFFF" font-family="Helvetica" font-size="9px" text-anchor="middle">returns</text></switch></g><path d="M 370 155 L 370 125 L 590 125 L 590 155" fill="#000000" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 370 155 L 370 235 L 590 235 L 590 155" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 370 155 L 590 155" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 140px; margin-left: 371px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#ffffff">SignalBuilder</font></div></div></div></foreignObject><text x="480" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">SignalBuilder</text></switch></g><rect x="370" y="155" width="220" height="80" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 210px; height: 1px; padding-top: 195px; margin-left: 376px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 76px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#ffffff">1) Modulator -&gt; clean basebanded signal<br />2) apply signal impairments (that belong here)<br /><br /><font face="Source Code Pro" data-font-src="https://fonts.googleapis.com/css?family=Source+Code+Pro" style="font-size: 10px;">builder.build()</font></font></div></div></div></foreignObject><text x="376" y="199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">1) Modulator -&gt; clean basebanded si...</text></switch></g><path d="M 310 180 L 363.63 180" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 368.88 180 L 361.88 183.5 L 363.63 180 L 361.88 176.5 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 181px; margin-left: 335px;"><div data-drawio-colors="color: #FFFFFF; background-color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; background-color: rgb(0, 0, 0); white-space: nowrap;">choose</div></div></div></foreignObject><text x="335" y="183" fill="#FFFFFF" font-family="Helvetica" font-size="9px" text-anchor="middle">choose</text></switch></g><rect x="40" y="117.5" width="270" height="125" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 180px; margin-left: 41px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%; font-size: 12px;"><font color="#ffffff">Choose random signal to generate</font></p><p style="line-height: 100%; font-size: 12px;"><font color="#ffffff">(user can define signal distribution/number of signals for wideband)</font></p><p style="line-height: 100%; font-size: 12px;"><span style="background-color: initial; font-size: 10px; font-family: &quot;Source Code Pro&quot;;"><font color="#ffffff">class_name = self._random_signal_class()</font></span></p><p style="line-height: 100%; font-size: 12px;"><span style="font-size: 10px; font-family: &quot;Source Code Pro&quot;; background-color: initial;"><font color="#ffffff">builder = self.builders[class_name]</font></span></p><p style="line-height: 100%;"></p><p style="line-height: 100%;"></p><p style="line-height: 100%; font-size: 12px;"></p><p style="line-height: 100%;"></p></div></div></div></foreignObject><text x="175" y="191" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="36px" text-anchor="middle">Choose random s...</text></switch></g><rect x="50" y="300" width="250" height="90" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 345px; margin-left: 51px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%; font-size: 12px;">place on narrowband/wideband cut</p><p style="line-height: 100%; font-size: 12px;"><span style="font-size: 10px; background-color: initial; font-family: &quot;Source Code Pro&quot;;">iq_samples[start:stop] += new_signal.data</span></p><p style="line-height: 100%;"></p><p style="line-height: 100%;"></p></div></div></div></foreignObject><text x="175" y="356" fill="#FFFFFF" font-family="Helvetica" font-size="36px" text-anchor="middle">place on narro...</text></switch></g><rect x="340.62" y="500" width="290" height="80" fill="#000000" stroke="#ffffff" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 288px; height: 1px; padding-top: 540px; margin-left: 342px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 36px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%; font-size: 12px;">Dataset Transforms: applied to whole IQ cut</p><p style="line-height: 100%; font-size: 12px;"><span style="font-size: 10px; font-family: &quot;Source Code Pro&quot;; background-color: initial;">sample = self.impairment_transforms(sample)</span></p><p style="line-height: 100%;"></p><p style="line-height: 100%;"></p></div></div></div></foreignObject><text x="486" y="551" fill="#FFFFFF" font-family="Helvetica" font-size="36px" text-anchor="middle">Dataset Transfor...</text></switch></g><path d="M 485.62 580 L 485.62 643.63" fill="none" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><path d="M 485.62 648.88 L 482.12 641.88 L 485.62 643.63 L 489.12 641.88 Z" fill="#ffffff" stroke="#ffffff" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 605px; margin-left: 486px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">apply</div></div></div></foreignObject><text x="486" y="607" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">apply</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>