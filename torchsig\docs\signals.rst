Signals
======================

Synthetic signal creation tools and builders used by both :class:`torchsig.datasets.wideband.NewWideband` 
and :class:`torchsig.datasets.narrowband.NewNarrowband` for dataset creation.

.. contents:: Signals
    :local:



Signal Class Types
--------------------------
.. automodule:: torchsig.signals.signal_types
    :members:
    :undoc-members:
    :show-inheritance:



Signal Builders
--------------------------

Builder
**************************
.. automodule:: torchsig.signals.builder
    :members:
    :undoc-members:
    :show-inheritance:

Specific Signal Builders
**************************
.. automodule:: torchsig.signals.builders
    :members:
    :undoc-members:
    :show-inheritance:



Signal Modulation Classes
--------------------------
.. automodule:: torchsig.signals.signal_lists
    :members:
    :undoc-members:
    :show-inheritance: