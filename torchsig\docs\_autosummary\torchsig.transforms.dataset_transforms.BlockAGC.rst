torchsig.transforms.dataset\_transforms.BlockAGC
================================================

.. currentmodule:: torchsig.transforms.dataset_transforms

.. autoclass:: BlockAGC
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~BlockAGC.add_parent
      ~BlockAGC.get_second_seed
      ~BlockAGC.seed
      ~BlockAGC.setup_rngs
      ~BlockAGC.update
      ~BlockAGC.update_from_parent
   
   

   
   
   