torchsig.datasets.datamodules.TorchSigDataModule
================================================

.. currentmodule:: torchsig.datasets.datamodules

.. autoclass:: TorchSigDataModule
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~TorchSigDataModule.from_datasets
      ~TorchSigDataModule.load_from_checkpoint
      ~TorchSigDataModule.load_state_dict
      ~TorchSigDataModule.on_after_batch_transfer
      ~TorchSigDataModule.on_before_batch_transfer
      ~TorchSigDataModule.on_exception
      ~TorchSigDataModule.predict_dataloader
      ~TorchSigDataModule.prepare_data
      ~TorchSigDataModule.save_hyperparameters
      ~TorchSigDataModule.setup
      ~TorchSigDataModule.state_dict
      ~TorchSigDataModule.teardown
      ~TorchSigDataModule.test_dataloader
      ~TorchSigDataModule.train_dataloader
      ~TorchSigDataModule.transfer_batch_to_device
      ~TorchSigDataModule.val_dataloader
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~TorchSigDataModule.CHECKPOINT_HYPER_PARAMS_KEY
      ~TorchSigDataModule.CHECKPOINT_HYPER_PARAMS_NAME
      ~TorchSigDataModule.CHECKPOINT_HYPER_PARAMS_TYPE
      ~TorchSigDataModule.hparams
      ~TorchSigDataModule.hparams_initial
      ~TorchSigDataModule.name
   
   