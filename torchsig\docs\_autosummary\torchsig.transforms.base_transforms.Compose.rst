torchsig.transforms.base\_transforms.Compose
============================================

.. currentmodule:: torchsig.transforms.base_transforms

.. autoclass:: Compose
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Compose.add_parent
      ~Compose.get_second_seed
      ~Compose.seed
      ~Compose.setup_rngs
      ~Compose.update
      ~Compose.update_from_parent
   
   

   
   
   