<mxfile host="65bd71144e" linkTarget="_blank" scale="1" border="0">
    <diagram id="LopdZo2FyCx790tcbxix" name="Page-1">
        <mxGraphModel dx="2948" dy="1902" grid="1" gridSize="12" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="16" value="&lt;p style=&quot;margin: 4px 0px 0px; text-align: center; font-size: 14px;&quot;&gt;&lt;b style=&quot;&quot;&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;Wideband&lt;/font&gt;&lt;/b&gt;&lt;/p&gt;&lt;hr style=&quot;font-size: 14px;&quot; size=&quot;1&quot;&gt;Init&lt;br&gt;+ FFT size&lt;br&gt;+ num iq samples&lt;br&gt;+ number of signals&lt;br&gt;&lt;hr&gt;Variables&lt;br&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ wideband_metadata: WidebandMetadata&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ signals: Signal[]&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;Functions&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ spectrogram(): computes&amp;nbsp; and returns spectrogram images&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ getters/setters for all parameters and objects&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="96" y="144" width="324" height="216" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;WidebandMetadata&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;fft size&lt;/p&gt;&lt;hr&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ fft_size&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ getters&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;" parent="1" vertex="1">
                    <mxGeometry x="528" y="198" width="160" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;DatasetMetadata&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;+ num_iq_samples&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ sample_rate&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ num signals&lt;/p&gt;&lt;hr&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ num_iq_samples_dataset&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ sample_rate&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ num_signals&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ noise_power = 1&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ getters for metadata&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;note: no setters for metadata&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;" parent="1" vertex="1">
                    <mxGeometry x="624" y="-186" width="180" height="252" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;fontSize=14;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="20" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="492" y="360" as="sourcePoint"/>
                        <mxPoint x="652" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="parent" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;fontSize=14;" parent="23" connectable="0" vertex="1">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="child" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;fontSize=14;" parent="23" connectable="0" vertex="1">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="Contains" style="endArrow=open;endSize=12;dashed=1;html=1;fontSize=14;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="16" target="17" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="432" y="216" as="sourcePoint"/>
                        <mxPoint x="592" y="216" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;NarrowbandMetadata&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ TBD&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ getters&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;" parent="1" vertex="1">
                    <mxGeometry x="756" y="192" width="160" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;fontSize=14;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="20" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="828" y="216" as="sourcePoint"/>
                        <mxPoint x="988" y="216" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="parent" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;fontSize=14;" parent="28" connectable="0" vertex="1">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="child" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;fontSize=14;" parent="28" connectable="0" vertex="1">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="Signal" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fontSize=14;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="-384" y="108" width="252" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="+ data: np.ndarray&#10;+ metadata: SignalMetadata&#10;+ dataset_metadata: DatasetMetadata" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;whiteSpace=wrap;" parent="31" vertex="1">
                    <mxGeometry y="26" width="252" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;fontSize=14;" parent="31" vertex="1">
                    <mxGeometry y="96" width="252" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="+ verify(): verifies data and metadata" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;whiteSpace=wrap;" parent="31" vertex="1">
                    <mxGeometry y="104" width="252" height="64" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="CompositeSignal" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fontSize=14;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="-744" y="108" width="252" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="+ signals: Signal[]&#10;+ metadata: SignalMetadata&#10;+ dataset_metadata: DatasetMetadata" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;whiteSpace=wrap;" parent="35" vertex="1">
                    <mxGeometry y="26" width="252" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;fontSize=14;" parent="35" vertex="1">
                    <mxGeometry y="96" width="252" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="+ verify(): verifies data and metadata" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;whiteSpace=wrap;" parent="35" vertex="1">
                    <mxGeometry y="104" width="252" height="64" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;fontSize=14;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="32" target="36" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-216" y="468" as="sourcePoint"/>
                        <mxPoint x="-140" y="420" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="parent" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;fontSize=14;" parent="39" connectable="0" vertex="1">
                    <mxGeometry x="-1" relative="1" as="geometry">
                        <mxPoint x="-36" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="child" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;fontSize=14;" parent="39" connectable="0" vertex="1">
                    <mxGeometry x="1" relative="1" as="geometry">
                        <mxPoint x="24" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="42" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;SignalMetadata&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ center_freq: float&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ bandwidth: float&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ start: float&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ duration: float&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ snr: float&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ class_name: str&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ class_index: int&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ rng: np.random.Generator&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ applied_transforms: Transform[]&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_dataset_metadata: DatasetMetadata&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ sample_rate() TBD&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ num_samples() TBD&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ stop()&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ upper_freq()&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ lower_freq()&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ samples_per_baud() TBD&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+verify(): verifies metadata&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="-96" y="444" width="204" height="312" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="Contains" style="endArrow=open;endSize=12;dashed=1;html=1;fontSize=14;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="32" target="42" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="-120" y="318" as="sourcePoint"/>
                        <mxPoint x="-24" y="318.0000000000002" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="Contains List of" style="endArrow=open;endSize=12;dashed=1;html=1;fontSize=14;exitX=-0.008;exitY=0.612;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="16" target="31" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="-240" y="325" as="sourcePoint"/>
                        <mxPoint x="-204" y="228" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;Builder&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;abstract class&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ name: str&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_signal: Signal&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;__init__()&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- inits name&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- call self.reset()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;* build(): returns built signal&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;* reset(): inits/resets self._signal&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-294" y="-528" width="180" height="192" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;SignalBuilder&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;abstract class&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ dataset_metadata&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;__init__()&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- calls super()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ build(): returns built signal&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- calls _create_data()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- calls _create_metadata()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- calls _signal.verify()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ reset(): inits/resets self._signal&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_create_data(): creates signal data&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_create_metadata(): creates signal metadata&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-324" y="-252" width="240" height="228" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="Generates" style="endArrow=open;endSize=12;dashed=1;html=1;fontSize=14;exitX=0.447;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="46" target="31" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="-240" y="132" as="sourcePoint"/>
                        <mxPoint x="-384" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="Uses" style="endArrow=open;endSize=12;dashed=1;html=1;fontSize=14;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="16" target="46" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="-372.72" y="96.86400000000003" as="sourcePoint"/>
                        <mxPoint x="-372" y="252" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;ToneSignalBuilder&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;variables TBD&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;__init__()&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- calls super()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_create_data(): implemented&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_create_metadata(): implemented&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;" parent="1" vertex="1">
                    <mxGeometry x="132" y="-264" width="192" height="144" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;fontSize=14;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="46" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="24" y="-168" as="sourcePoint"/>
                        <mxPoint x="184" y="-168" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="52" value="parent" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;fontSize=14;" parent="51" connectable="0" vertex="1">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="child" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;fontSize=14;" parent="51" connectable="0" vertex="1">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;fontSize=14;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="45" target="46" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-72" y="-186" as="sourcePoint"/>
                        <mxPoint x="144" y="-180" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="parent" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;fontSize=14;" parent="54" connectable="0" vertex="1">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="child" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;fontSize=14;" parent="54" connectable="0" vertex="1">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;b&gt;CompositeSignalBuilder&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;+ builders: SignalBuilder[]&lt;/span&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;__init__()&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- calls super()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ build(): returns built composite signal&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- recursively calls each SignalBuilder&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- calls _signal.verify()&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ reset(): inits/resets self._signal as CompositeSignal&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_create_data(): creates signal data&lt;/p&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;_create_metadata(): creates signal metadata&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-762" y="-234" width="288" height="192" as="geometry"/>
                </mxCell>
                <mxCell id="58" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;fontSize=14;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="46" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-360" y="-144.52999999999997" as="sourcePoint"/>
                        <mxPoint x="-444" y="-240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="parent" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;fontSize=14;" parent="58" connectable="0" vertex="1">
                    <mxGeometry x="-1" relative="1" as="geometry">
                        <mxPoint x="-36" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="60" value="child" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;fontSize=14;" parent="58" connectable="0" vertex="1">
                    <mxGeometry x="1" relative="1" as="geometry">
                        <mxPoint x="24" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="61" value="Generates" style="endArrow=open;endSize=12;dashed=1;html=1;fontSize=14;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="57" target="35" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="-606.72" y="-23.997999999999966" as="sourcePoint"/>
                        <mxPoint x="-648" y="107.09000000000023" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="transforms/base_transforms.py" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=36;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fontSize=14;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="-1104" y="432" width="252" height="132" as="geometry"/>
                </mxCell>
                <mxCell id="63" value="Transform&#10;Compose&#10;Normalize&#10;RandomApply&#10;RandAugment" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;whiteSpace=wrap;" vertex="1" parent="62">
                    <mxGeometry y="36" width="252" height="96" as="geometry"/>
                </mxCell>
                <mxCell id="69" value="transforms/ml_transforms.py" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=36;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fontSize=14;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="-840" y="432" width="252" height="204" as="geometry"/>
                </mxCell>
                <mxCell id="70" value="TimeReversal&#10;ChannelSwap&#10;AddSlope&#10;RandomMagRescale&#10;RandomDropSamples&#10;CutOut&#10;PatchShuffle&#10;DropSpectrogram&#10;SpectrogramPatchShuffle" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;whiteSpace=wrap;" vertex="1" parent="69">
                    <mxGeometry y="36" width="252" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="71" value="transforms/signal_impairments.py" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=36;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fontSize=14;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="-576" y="432" width="252" height="468" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="PassbandRipples&#10;IQImbalance&#10;DCOffset&#10;LocalOscillatorPhaseNoise&#10;LocalOscillatorFrequencyDrift&#10;PowerAmplifierCompression&#10;ADCSaturation&#10;SlowAGC&#10;BlackAGC&#10;Quantize&#10;SamplingClockError&#10;FastAGC&#10;SpectralInversion&#10;Doppler&#10;AWGN&#10;ColoredNoise&#10;TimeVaryingNoise&#10;ImpulsiveNoise&#10;FrequencySelectiveFading&#10;Fading&#10;PhaseOffset&#10;AdjacentChannelInterference&#10;CoChannleInterference&#10;TimeShift&#10;AtmosphericDuct" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;whiteSpace=wrap;" vertex="1" parent="71">
                    <mxGeometry y="36" width="252" height="432" as="geometry"/>
                </mxCell>
                <mxCell id="73" value="Transforms/Impairments" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=36;" vertex="1" parent="1">
                    <mxGeometry x="-906" y="354" width="384" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>