torchsig.models.model\_utils.general\_layers.Reshape
====================================================

.. currentmodule:: torchsig.models.model_utils.general_layers

.. autoclass:: Reshape
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~Reshape.add_module
      ~Reshape.apply
      ~Reshape.bfloat16
      ~Reshape.buffers
      ~Reshape.children
      ~Reshape.compile
      ~Reshape.cpu
      ~Reshape.cuda
      ~Reshape.double
      ~Reshape.eval
      ~Reshape.extra_repr
      ~Reshape.float
      ~Reshape.forward
      ~Reshape.get_buffer
      ~Reshape.get_extra_state
      ~Reshape.get_parameter
      ~Reshape.get_submodule
      ~Reshape.half
      ~Reshape.ipu
      ~Reshape.load_state_dict
      ~Reshape.modules
      ~Reshape.mtia
      ~Reshape.named_buffers
      ~Reshape.named_children
      ~Reshape.named_modules
      ~Reshape.named_parameters
      ~Reshape.parameters
      ~Reshape.register_backward_hook
      ~Reshape.register_buffer
      ~Reshape.register_forward_hook
      ~Reshape.register_forward_pre_hook
      ~Reshape.register_full_backward_hook
      ~Reshape.register_full_backward_pre_hook
      ~Reshape.register_load_state_dict_post_hook
      ~Reshape.register_load_state_dict_pre_hook
      ~Reshape.register_module
      ~Reshape.register_parameter
      ~Reshape.register_state_dict_post_hook
      ~Reshape.register_state_dict_pre_hook
      ~Reshape.requires_grad_
      ~Reshape.set_extra_state
      ~Reshape.set_submodule
      ~Reshape.share_memory
      ~Reshape.state_dict
      ~Reshape.to
      ~Reshape.to_empty
      ~Reshape.train
      ~Reshape.type
      ~Reshape.xpu
      ~Reshape.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~Reshape.T_destination
      ~Reshape.call_super_init
      ~Reshape.dump_patches
      ~Reshape.training
   
   