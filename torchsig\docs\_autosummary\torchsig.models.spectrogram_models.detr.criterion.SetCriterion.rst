torchsig.models.spectrogram\_models.detr.criterion.SetCriterion
===============================================================

.. currentmodule:: torchsig.models.spectrogram_models.detr.criterion

.. autoclass:: SetCriterion
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~SetCriterion.add_module
      ~SetCriterion.apply
      ~SetCriterion.bfloat16
      ~SetCriterion.buffers
      ~SetCriterion.children
      ~SetCriterion.compile
      ~SetCriterion.cpu
      ~SetCriterion.cuda
      ~SetCriterion.double
      ~SetCriterion.eval
      ~SetCriterion.extra_repr
      ~SetCriterion.float
      ~SetCriterion.forward
      ~SetCriterion.get_buffer
      ~SetCriterion.get_extra_state
      ~SetCriterion.get_loss
      ~SetCriterion.get_parameter
      ~SetCriterion.get_submodule
      ~SetCriterion.half
      ~SetCriterion.ipu
      ~SetCriterion.load_state_dict
      ~SetCriterion.loss_labels
      ~SetCriterion.loss_masks
      ~SetCriterion.modules
      ~SetCriterion.mtia
      ~SetCriterion.named_buffers
      ~SetCriterion.named_children
      ~SetCriterion.named_modules
      ~SetCriterion.named_parameters
      ~SetCriterion.parameters
      ~SetCriterion.register_backward_hook
      ~SetCriterion.register_buffer
      ~SetCriterion.register_forward_hook
      ~SetCriterion.register_forward_pre_hook
      ~SetCriterion.register_full_backward_hook
      ~SetCriterion.register_full_backward_pre_hook
      ~SetCriterion.register_load_state_dict_post_hook
      ~SetCriterion.register_load_state_dict_pre_hook
      ~SetCriterion.register_module
      ~SetCriterion.register_parameter
      ~SetCriterion.register_state_dict_post_hook
      ~SetCriterion.register_state_dict_pre_hook
      ~SetCriterion.requires_grad_
      ~SetCriterion.set_extra_state
      ~SetCriterion.set_submodule
      ~SetCriterion.share_memory
      ~SetCriterion.state_dict
      ~SetCriterion.to
      ~SetCriterion.to_empty
      ~SetCriterion.train
      ~SetCriterion.type
      ~SetCriterion.xpu
      ~SetCriterion.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~SetCriterion.T_destination
      ~SetCriterion.call_super_init
      ~SetCriterion.dump_patches
      ~SetCriterion.training
   
   