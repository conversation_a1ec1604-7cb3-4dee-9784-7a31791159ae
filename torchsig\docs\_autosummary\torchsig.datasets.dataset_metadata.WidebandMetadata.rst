torchsig.datasets.dataset\_metadata.WidebandMetadata
====================================================

.. currentmodule:: torchsig.datasets.dataset_metadata

.. autoclass:: WidebandMetadata
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~WidebandMetadata.add_parent
      ~WidebandMetadata.get_second_seed
      ~WidebandMetadata.seed
      ~WidebandMetadata.setup_rngs
      ~WidebandMetadata.to_dict
      ~WidebandMetadata.update_from_parent
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~WidebandMetadata.bandwidth_max
      ~WidebandMetadata.bandwidth_min
      ~WidebandMetadata.center_freq_max
      ~WidebandMetadata.center_freq_min
      ~WidebandMetadata.class_distribution
      ~WidebandMetadata.class_list
      ~WidebandMetadata.dataset_type
      ~WidebandMetadata.duration_in_samples_max
      ~WidebandMetadata.duration_in_samples_min
      ~WidebandMetadata.fft_frequency_max
      ~WidebandMetadata.fft_frequency_min
      ~WidebandMetadata.fft_frequency_resolution
      ~WidebandMetadata.fft_size
      ~WidebandMetadata.fft_stride
      ~WidebandMetadata.frequency_max
      ~WidebandMetadata.frequency_min
      ~WidebandMetadata.impairment_level
      ~WidebandMetadata.impairments
      ~WidebandMetadata.minimum_params
      ~WidebandMetadata.noise_power_db
      ~WidebandMetadata.num_iq_samples_dataset
      ~WidebandMetadata.num_samples
      ~WidebandMetadata.num_samples_generated
      ~WidebandMetadata.num_signals_distribution
      ~WidebandMetadata.num_signals_max
      ~WidebandMetadata.num_signals_min
      ~WidebandMetadata.num_signals_range
      ~WidebandMetadata.sample_rate
      ~WidebandMetadata.signal_duration_percent_max
      ~WidebandMetadata.signal_duration_percent_min
      ~WidebandMetadata.snr_db_max
      ~WidebandMetadata.snr_db_min
      ~WidebandMetadata.target_transforms
      ~WidebandMetadata.transforms
   
   