torchsig.models.model\_utils.model\_utils\_1d.layers\_1d.FastGlobalAvgPool1d
============================================================================

.. currentmodule:: torchsig.models.model_utils.model_utils_1d.layers_1d

.. autoclass:: FastGlobalAvgPool1d
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~FastGlobalAvgPool1d.add_module
      ~FastGlobalAvgPool1d.apply
      ~FastGlobalAvgPool1d.bfloat16
      ~FastGlobalAvgPool1d.buffers
      ~FastGlobalAvgPool1d.children
      ~FastGlobalAvgPool1d.compile
      ~FastGlobalAvgPool1d.cpu
      ~FastGlobalAvgPool1d.cuda
      ~FastGlobalAvgPool1d.double
      ~FastGlobalAvgPool1d.eval
      ~FastGlobalAvgPool1d.extra_repr
      ~FastGlobalAvgPool1d.float
      ~FastGlobalAvgPool1d.forward
      ~FastGlobalAvgPool1d.get_buffer
      ~FastGlobalAvgPool1d.get_extra_state
      ~FastGlobalAvgPool1d.get_parameter
      ~FastGlobalAvgPool1d.get_submodule
      ~FastGlobalAvgPool1d.half
      ~FastGlobalAvgPool1d.ipu
      ~FastGlobalAvgPool1d.load_state_dict
      ~FastGlobalAvgPool1d.modules
      ~FastGlobalAvgPool1d.mtia
      ~FastGlobalAvgPool1d.named_buffers
      ~FastGlobalAvgPool1d.named_children
      ~FastGlobalAvgPool1d.named_modules
      ~FastGlobalAvgPool1d.named_parameters
      ~FastGlobalAvgPool1d.parameters
      ~FastGlobalAvgPool1d.register_backward_hook
      ~FastGlobalAvgPool1d.register_buffer
      ~FastGlobalAvgPool1d.register_forward_hook
      ~FastGlobalAvgPool1d.register_forward_pre_hook
      ~FastGlobalAvgPool1d.register_full_backward_hook
      ~FastGlobalAvgPool1d.register_full_backward_pre_hook
      ~FastGlobalAvgPool1d.register_load_state_dict_post_hook
      ~FastGlobalAvgPool1d.register_load_state_dict_pre_hook
      ~FastGlobalAvgPool1d.register_module
      ~FastGlobalAvgPool1d.register_parameter
      ~FastGlobalAvgPool1d.register_state_dict_post_hook
      ~FastGlobalAvgPool1d.register_state_dict_pre_hook
      ~FastGlobalAvgPool1d.requires_grad_
      ~FastGlobalAvgPool1d.set_extra_state
      ~FastGlobalAvgPool1d.set_submodule
      ~FastGlobalAvgPool1d.share_memory
      ~FastGlobalAvgPool1d.state_dict
      ~FastGlobalAvgPool1d.to
      ~FastGlobalAvgPool1d.to_empty
      ~FastGlobalAvgPool1d.train
      ~FastGlobalAvgPool1d.type
      ~FastGlobalAvgPool1d.xpu
      ~FastGlobalAvgPool1d.zero_grad
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~FastGlobalAvgPool1d.T_destination
      ~FastGlobalAvgPool1d.call_super_init
      ~FastGlobalAvgPool1d.dump_patches
      ~FastGlobalAvgPool1d.training
   
   