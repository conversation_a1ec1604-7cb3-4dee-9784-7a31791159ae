# TorchSig Examples
This folder contains sample Jupyter Notebooks that demonstrate some of the capabilities of TorchSig.

| Notebook | Description  |
| -------- | -----------  |
| getting_started.ipynb | TorchSig overview, description, and terms. |
| narrowband_example.ipynb | Introduction to Narrowband. |
| narrowband_classifier_example.ipynb | Training a model for modulation recognition on Narrowband. |
| wideband_example.ipynb | Introduction to Wideband. |
| wideband_detector_example.ipynb | Training a YOLO model for signal detection on Wideband. |
| synthetic_spectrogram_image_dataset.ipynb | Introduction to synthetic spectrogram drawing. |