torchsig.image\_datasets.transforms.impairments.RippleNoiseTransform
====================================================================

.. currentmodule:: torchsig.image_datasets.transforms.impairments

.. autoclass:: RippleNoiseTransform
   :members:
   :show-inheritance:
   :inherited-members:
   :special-members: __call__, __repr__, __str__, __init__

   
   
   .. rubric:: Methods

   .. autosummary::
      :nosignatures:
   
      ~RippleNoiseTransform.add_parent
      ~RippleNoiseTransform.get_second_seed
      ~RippleNoiseTransform.seed
      ~RippleNoiseTransform.setup_rngs
      ~RippleNoiseTransform.update_from_parent
      ~RippleNoiseTransform.update_mesh_spacing
   
   

   
   
   